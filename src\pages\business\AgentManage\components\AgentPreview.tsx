import { Image } from 'antd';
import { AgentFigureTypeEnum } from '@/common/utils/enum';
import { ProDescriptions } from '@ant-design/pro-components';
import React from 'react';

interface AgentPreviewProps {
  data: {
    name?: string;
    iconUrl?: any;
    introduction?: string;
    openerPrompt?: string;
  };
}

const AgentPreview: React.FC<AgentPreviewProps> = ({ data }) => {
  return (
    <div style={{ display: 'flex', gap: '24px', minHeight: '650px', width: '100%' }}>
      {/* 左侧信息内容 */}
      <div
        style={{
          flex: '0 0 50%',
        }}
      >
        <ProDescriptions title="基础信息" dataSource={data} column={3} layout="vertical">
          <ProDescriptions.Item label="名称" dataIndex="name" />
          <ProDescriptions.Item label="形象类型">
            {AgentFigureTypeEnum?.[(data as any)?.figureType] || '-'}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="形象">
            <Image
              src={
                Array.isArray(data?.iconUrl) ? data?.iconUrl[0]?.fileUrl : (data?.iconUrl as any)
              }
              style={{ maxWidth: 120, maxHeight: 120, objectFit: 'contain' }}
            />
          </ProDescriptions.Item>
        </ProDescriptions>

        <ProDescriptions dataSource={data} column={1} layout="vertical">
          <ProDescriptions.Item label="简介" dataIndex="introduction" />
          <ProDescriptions.Item label="开场白" dataIndex="openerPrompt" />
        </ProDescriptions>
      </div>

      {/* 右侧对话预览 */}
      <div
        style={{
          flex: '0 0 50%',
          width: 420,
          padding: '20px',
          backgroundColor: '#fff',
          borderRadius: '8px',
          borderLeft: '1px solid #e8e8e8',
        }}
      >
        <h3
          style={{
            marginBottom: '20px',
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#1890ff',
          }}
        >
          对话预览
        </h3>
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <iframe
            src="https://test.shukeyun.com/scenic/shop/#/pages/ai/aiAgent?id=7369267935220604928&storeId=748963187789558116"
            style={{ width: 300, height: '600px', border: 0, borderRadius: 8 }}
          />
        </div>
      </div>
    </div>
  );
};

export default AgentPreview;
