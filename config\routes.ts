﻿/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn，注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */

export default [
  /** 菜单页 */
  /** 【首页】 */
  {
    path: 'welcome',
    name: '首页',
    icon: 'home',
    id: 'uX4tQ9rF',
    component: 'Welcome',
    access: 'canHomePage',
  },
  {
    name: '合作伙伴管理',
    id: 'jG6zP3vW',
    path: 'partner',
    icon: 'TeamOutlined',
    routes: [
      {
        name: '我是分销商',
        path: 'supply-chain',
        id: 'mD1vN7sL',
        routes: [
          {
            name: '供应商管理',
            path: 'supplier',
            id: 'bF3qR9tM',
            component: 'partner/supplyChain/Supplier',
            access: 'canSupplierManagement',
          },
          {
            name: '采购订单',
            path: 'purchase-order',
            id: 'lJ5wT8yK',
            component: 'partner/supplyChain/PurchaseOrder',
            access: 'canPurchaseOrder',
          },
          {
            name: '采购退单',
            path: 'purchase-return',
            id: 'pM9sG4vW',
            component: 'partner/supplyChain/PurchaseReturn',
            access: 'canPurchaseRefundOrder',
          },
        ],
      },
      {
        name: '我是供应商',
        id: 'cL2xN6yQ',
        path: 'sale-channel',
        routes: [
          {
            name: '销售管理',
            path: 'management',
            id: 'nK3yS6vT',
            component: 'partner/saleChannel/Management',
            access: 'canAgentGroupManagement',
          },
          {
            name: '销售分组',
            path: 'group',
            id: 'wR7hV1qS',
            component: 'partner/saleChannel/Group',
            access: 'canSupplierGroup',
          },

          {
            name: '价格策略',
            path: 'price-strategy',
            id: 'hV3sJ6mW',
            component: 'partner/saleChannel/PriceStrategy',
            access: 'canPriceStrategy',
          },
          {
            name: '设置价格策略',
            path: 'price-strategy/strategy-list/:id',
            component: 'partner/saleChannel/PriceStrategy/StrategyList',
            access: 'canPriceStrategy',
            hideInMenu: true,
          },
          {
            name: '发放的佣金账单',
            path: 'commission-bill',
            id: 'qP9tK4zH',
            component: 'partner/saleChannel/CommissionBill',
            access: 'canComissionBill',
          },
          {
            name: '销售订单',
            path: 'sales-order',
            id: 'bR5wS8vF',
            component: 'partner/saleChannel/SaleOrder',
            access: 'canSalesOrder',
          },
          {
            name: '绩效统计',
            path: 'performance-statistics',
            id: 'bR5wS8gg',
            component: 'partner/saleChannel/PerformanceStatistics',
            access: 'canSalesOrder',
          },
        ],
      },
      {
        name: '合作邀请审核',
        icon: 'FileDoneOutlined',
        path: 'audit-manage',
        id: 'gN2xW7yM',
        component: 'partner/AuditManage',
        access: 'canCheckManagement',
      },
    ],
  },

  {
    name: 'C 端业务管理',
    path: 'business',
    id: 'sH8rT2wN',
    icon: 'DesktopOutlined',
    routes: [
      {
        name: '虚拟导游管理',
        path: 'agent-manage',
        component: 'business/AgentManage',
        id: 'wS8tM2LL',
        access: 'agentManagement',
      },
      {
        name: '店铺管理',
        path: 'my-shop',
        component: 'business/MyShop',
        id: 'wS8tM2qJ',
        access: 'canMyShop',
        exact: false,
      },
      {
        name: 'C 端订单管理',
        path: 'order-management',
        id: 'cH5yD7rK',
        icon: 'UserSwitchOutlined',
        routes: [
          {
            path: 'order',
            name: '库存销售记录',
            icon: 'AccountBookOutlined',
            id: 'lT6mX9wP',
            component: 'business/orderManagement/Sale',
            access: 'canOrderManage',
          },
          {
            path: 'refund',
            name: '库存退单记录',
            id: 'dV1wK4sM',
            icon: 'FileSearchOutlined',
            component: 'business/orderManagement/Refund',
            access: 'canRefundOrderManage',
          },
        ],
      },
      {
        name: '批量发权益卡',
        icon: 'FileDoneOutlined',
        path: 'card', //Special authority card
        component: 'business/SACard',
        id: 'hJ8vT5rD',
        access: 'canCheckManagement', //2023 年 12 月 20 日 后期再统一加权限控制
      },
      {
        name: '批量导入',
        icon: 'AccountBookOutlined',
        path: 'saCardManageAdd',
        component: './business/SACard/Add',
        hideInMenu: true,
        id: 'sF3qN7tR',
      },
    ],
  },

  /** 【财务管理管理】 */
  {
    path: 'financial-management',
    name: '财务管理',
    id: 'bM4wV9xG',
    icon: 'PayCircleOutlined',
    routes: [
      {
        name: '财务账单',
        path: 'statement',
        id: 'tQ7yP2vS',
        component: './financialManagement/FinancialStatement',
        access: 'canFinancialStatement',
      },
      {
        name: '授信管理',
        id: 'kX6mH1wZ',
        path: 'credit-management',
        routes: [
          {
            name: '授信账户',
            path: 'customer',
            id: 'qF6sX3gR',
            component: './financialManagement/creditManagement/CreditCustomer',
            access: 'canCreditCustomerManage',
          },
          {
            name: '授信操作记录',
            path: 'operation-record',
            id: 'vT9uP7wL',
            component: './financialManagement/creditManagement/CreditOperationRecord',
            access: 'canCreditRecord',
          },
          {
            name: '授信结算单',
            path: 'settlement',
            id: 'dG5qK8wR',
            component: './financialManagement/creditManagement/CreditSettlement',
            access: 'canCreditBill',
          },
          {
            name: '还款账户',
            path: 'repay-account',
            id: 'pM2xY4cV',
            component: './financialManagement/accountManagement/CreditAccount',
            access: 'canCreditAccount',
          },
          {
            name: '还款记录',
            path: 'repay-record',
            id: 'lN8rV1sF',
            component: './financialManagement/accountManagement/RepaymentRecord',
            access: 'canReturnRecord_detail',
          },
        ],
      },
      // {
      //   name: '预授信管理',
      //   id: 'kX6mH1wZ2',
      //   path: 'advance-credit',
      //   routes: [
      //     {
      //       name: '授信机构',
      //       path: 'credit-providers',
      //       id: 'qF6sX3gR2',
      //       component: './financialManagement/advanceCreditManagement/CreditProviders',
      //     },
      //     {
      //       name: '交易记录',
      //       path: 'trading-record',
      //       id: 'vT9uP7wL2',
      //       component: './financialManagement/advanceCreditManagement/TradingRecord',
      //     },
      //     {
      //       name: '还款记录',
      //       path: 'repayment-record',
      //       id: 'dG5qK8wR2',
      //       component: './financialManagement/advanceCreditManagement/RepaymentRecord',
      //     },
      //     {
      //       name: '余额记录',
      //       path: 'balance-record',
      //       id: 'pM2xY4cV2',
      //       component: './financialManagement/advanceCreditManagement/BalanceRecord',
      //     },
      //     {
      //       name: '额度记录',
      //       path: 'limit-record',
      //       id: 'lN8rV1sF2',
      //       component: './financialManagement/advanceCreditManagement/LimitRecord',
      //     },
      //   ],
      // },
      {
        name: '佣金管理',
        path: 'commission-management',
        id: 'hJ7yA6tD',
        // access: 'canaccountManagement',
        routes: [
          {
            name: '获得的佣金账单',
            path: 'settlement-registration',
            id: 'kL4wT9xP',
            component: './financialManagement/billManagement/CommissionBill',
            access: 'canBalanceAssignment',
          },
          {
            name: '订单详情',
            hideInMenu: true,
            id: 'rW1zH3vQ',
            path: 'order-detail/:id',
            component: './financialManagement/billManagement/CommissionBill/OrderDetail',
          },
        ],
      },
      {
        name: '直销管理',
        path: 'selling-management',
        id: 'tS2uB5mF',
        access: 'canFinancialAccount',
        routes: [
          {
            name: '现金账单',
            path: 'cash-statement',
            id: 'cD8vQ6rW',
            component: './financialManagement/saleManagement/cashStatement',
            access: 'canBillAccount',
          },
        ],
      },
    ],
  },

  /** 【数据统计】 */
  {
    name: '数据统计',
    path: 'statistic',
    id: 'dP9mG2vS',
    icon: 'LineChartOutlined',
    routes: [
      {
        name: '我的库存查询',
        path: 'stock',
        id: 'qF5wT7rJ',
        icon: 'UserSwitchOutlined',
        routes: [
          {
            name: '票务库存',
            id: 'jN6tL4yH',
            path: 'distributorProduct',
            component: 'statistic/stock/Ticket',
            access: 'canDistributeProcduct',
          },
          {
            name: '库存预警',
            id: 'jN6tL4yB',
            path: 'InventoryAlert',
            component: 'statistic/stock/InventoryAlert',
            access: 'canDistributeProcduct',
          },
        ],
      },
      {
        name: '订单汇总 / 明细',
        path: 'order',
        id: 'xW3pV7sK',
        component: 'statistic/OrderSummary',
        access: 'canOrderSummaryDetails',
      },
    ],
  },
  /** 【企业管理】 */
  {
    name: '企业管理',
    path: 'enterprise',
    id: 'lZ8qR1tG',
    icon: 'BankOutlined',
    component: 'Enterprise',
    access: 'canEnterpriceManage',
  },

  /** 【用户中心】 */
  {
    name: '用户中心',
    path: 'user',
    id: 'hS2vF5yD',
    icon: 'UserSwitchOutlined',
    routes: [
      {
        name: '用户管理',
        id: 'gM4rT9wQ',
        icon: 'ClusterOutlined',
        path: 'manage',
        component: 'user/Manage',
        access: 'canOrganizationManage',
      },
      {
        name: '权限管理',
        icon: 'UsergroupAddOutlined',
        path: 'authority',
        id: 'cX7sK3mV',
        component: 'user/Authority',
        access: 'canRoleManage',
      },
      {
        name: '用户审批',
        icon: 'StepBackwardOutlined',
        id: 'bD1tW8yN',
        path: 'approve',
        component: 'user/Approve',
        access: 'canUserApprove',
      },
    ],
  },

  /** 【系统设置】 */
  {
    name: '系统设置',
    icon: 'SettingOutlined',
    id: 'vG6hJ2zR',
    path: 'system',
    routes: [
      {
        name: '销售助手',
        path: 'guide',
        id: 'uX4tQ9rF',
        component: 'system/Guide',
        access: 'canHomePage',
      },
      {
        name: '风控管理',
        path: 'risk-control',
        id: 'qX8rY3vT',
        component: 'system/RiskControl',
        access: 'canRiskManagement',
      },
      {
        name: '操作日志',
        path: 'operationLog',
        id: 'wN5tR7yQ',
        component: 'system/OperationLog',
        access: 'canOperationLog',
      },
      {
        name: '下载中心',
        path: 'down',
        component: 'system/Down',
      },
    ],
  },

  {
    layout: false,
    hideInMenu: true,
    name: '接受邀请',
    path: 'invite',
    component: 'Invite',
    menuRender: false,
  },

  {
    layout: false,
    hideInMenu: true,
    name: '用户申请',
    path: 'application-approved',
    component: 'ApplicationApproved',
  },
  {
    layout: false,
    hideInMenu: true,
    name: '用户申请加入企业',
    icon: 'StepBackwardOutlined',
    path: 'UserApplies',
    component: 'UserApplies',
  },
  {
    name: '申请加入企业',
    path: 'companyJoin',
    component: 'CompanyJoin',
    menuRender: false,
    hideInMenu: true,

    routes: [
      {
        name: '申请',
        path: 'apply',
        component: 'CompanyJoin/Apply',
      },
      {
        name: '提交',
        path: 'commit',
        component: 'CompanyJoin/Commit',
      },
      {
        name: '结果',
        path: 'result',
        component: 'CompanyJoin/Result',
      },
    ],
  },

  // 提示升级版本
  {
    path: 'upgrade',
    name: '升级版本',
    layout: false,
    hideInMenu: true,
    component: './UpgradeVersion',
  },

  {
    layout: false,
    hideInMenu: true,
    path: 'result',
    component: './Result',
  },
  { path: '/', redirect: '/welcome' },
  { path: '*', component: './404' },
];
