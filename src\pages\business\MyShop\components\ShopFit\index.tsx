import template0 from '@/assets/template0.jpg';
import template1 from '@/assets/template1.jpg';
import template2 from '@/assets/template2.jpg';
import template3 from '@/assets/template3.jpg';
import template4 from '@/assets/template4.jpg';
import template5 from '@/assets/template5.png';
import Delete from '@/common/components/Delete';
import { tableConfig } from '@/common/utils/config';
import { getEnv } from '@/common/utils/getEnv';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { batchDeleteFit, deleteFit, pageFit, stateFit } from '@/services/api/shopFit';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Card, Modal, Space, Tag, Typography, message } from 'antd';
import Meta from 'antd/lib/card/Meta';
import { useContext, useRef, useState } from 'react';
import { TabKeyContext } from '../..';
import styles from './index.less';

export default ({ storeId }: any) => {
  const actionRef = useRef<ActionType>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const tabKey = useContext(TabKeyContext);
  const { FIT_URL } = getEnv();
  const coId = localStorage.getItem('currentCompanyId');

  const tableColumns: ProColumnType[] = [
    {
      title: '页面名称',
      dataIndex: 'pageName',
      render: (dom: any, entity: any) => (
        <Space>
          {dom}
          {entity.homePageState == 2 && <Tag color="blue">主页</Tag>}
        </Space>
      ),
    },
    {
      title: '页面状态',
      dataIndex: 'pageState',
      valueEnum: { 1: '未发布', 2: '已发布' },
    },
    {
      title: '有无草稿',
      dataIndex: 'pageDraftState',
      valueEnum: { 1: '有草稿', 2: '无草稿' },
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      search: false,
    },
    {
      title: '操作用户',
      dataIndex: 'modifyUserName',
      search: false,
    },
    {
      title: '页面备注',
      dataIndex: 'remark',
      search: false,
      renderText: (text: any) => (
        <Typography.Paragraph ellipsis style={{ maxWidth: 200, marginBottom: 0 }}>
          {text}
        </Typography.Paragraph>
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_: any, entity: any) => [
        <a
          key="edit"
          href={`${FIT_URL}?coId=${coId}&storeId=${storeId}&pageId=${entity.id}&type=edit`}
          target="_blank"
          rel="noreferrer"
        >
          编辑
        </a>,
        <a
          key="copy"
          href={`${FIT_URL}?coId=${coId}&storeId=${storeId}&pageId=${entity.id}&type=copy`}
          target="_blank"
          rel="noreferrer"
        >
          复制
        </a>,
        entity.homePageState != 2 && (
          <Delete
            key="delete"
            access={true}
            status={entity.homePageState == 2}
            params={{ id: entity.id }}
            request={async (params: any) => {
              const data = await deleteFit(params);

              addOperationLogRequest({
                action: 'del',
                module: tabKey,
                content: `删除【${entity.pageName}】页面`,
              });
              return data;
            }}
            actionRef={actionRef}
            content="主页不可删除！"
          />
        ),
        entity.homePageState == 1 && entity.pageState == 2 && (
          <a
            key="setting"
            onClick={() => {
              stateFit({ id: entity.id }).then(() => {
                addOperationLogRequest({
                  action: 'edit',
                  module: tabKey,
                  content: `设置页面【${entity.pageName}】为主页`,
                });
                actionRef.current?.reload();
              });
            }}
          >
            设为主页
          </a>
        ),
      ],
    },
  ];
  const addButton = (
    <Button
      key="addButton"
      type="primary"
      onClick={() => {
        setIsModalOpen(true);
      }}
    >
      <PlusOutlined /> 新增
    </Button>
  );
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const cardList = [
    {
      title: '空白模板',
      cover: template0,
    },
    {
      title: '通用模板',
      cover: template1,
    },
    {
      title: '行业智能体模版',
      cover: template5,
    },
    {
      title: '自然类景区',
      cover: template2,
    },
    {
      title: '乐园类景区',
      cover: template3,
    },
    {
      title: '文化类景区',
      cover: template4,
    },
  ];
  return (
    <>
      <ProTable
        {...tableConfig}
        rowKey="id"
        rowSelection={{
          getCheckboxProps: (record: any) => ({
            disabled: record.homePageState == 2,
          }),
        }}
        tableAlertRender={({ selectedRowKeys, onCleanSelected }) => (
          <Space size={8}>
            <span>已选 {selectedRowKeys.length} 项</span>
            <a onClick={onCleanSelected}>取消选择</a>
          </Space>
        )}
        tableAlertOptionRender={({ selectedRowKeys, onCleanSelected }) => (
          <a
            onClick={() => {
              batchDeleteFit(selectedRowKeys.join(',')).then(() => {
                message.success('删除成功');
                actionRef.current?.reload();
                onCleanSelected();
              });
            }}
          >
            批量删除
          </a>
        )}
        actionRef={actionRef}
        columns={tableColumns}
        toolBarRender={() => [addButton]}
        params={{ storeId }}
        request={pageFit}
      />
      <Modal
        width={1348}
        title="选择模板"
        open={isModalOpen}
        onCancel={handleCancel}
        footer={false}
      >
        <Space size={[20, 20]} wrap>
          {cardList.map((item: any, index: any) => (
            <Card
              key={index}
              hoverable
              cover={
                <div
                  style={{
                    width: 200,
                    height: 392,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    position: 'relative',
                    borderColor: 'inherit',
                    overflow: 'hidden',
                  }}
                >
                  <img
                    style={{ maxWidth: '100%', position: index ? 'absolute' : 'unset', inset: 0 }}
                    src={item.cover}
                  />
                  <div
                    style={{
                      position: 'absolute',
                      inset: 0,
                      border: '1px solid',
                      borderColor: 'inherit',
                      borderBottomWidth: 0,
                    }}
                  />
                </div>
              }
              actions={[<Meta title={item.title} />]}
              onClick={() => {
                open(`${FIT_URL}?coId=${coId}&storeId=${storeId}&type=add&template=${index}`);
              }}
              bodyStyle={{ display: 'none' }}
              className={styles.cardStyle}
            />
          ))}
        </Space>
      </Modal>
    </>
  );
};
