import React, { useState, useEffect } from 'react';
import {
  Modal,
  Radio,
  Input,
  Checkbox,
  Button,
  Tooltip,
  DatePicker,
  Tag,
  Space,
  InputNumber,
  message,
  Spin,
  Form,
  Row,
  Col,
  Empty,
} from 'antd';
import { InfoCircleOutlined, CloseOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import './PriceSettingModal.css';
import type { API } from '@/services/api/typings';
import { ceil } from 'lodash';
import GoodsTable from './components/GoodsTable';
import PriceHistoryChart from './components/PriceHistoryChart';
import WeatherTrendChart from './components/WeatherTrendChart';
import ParkEntryChart from './components/ParkEntryChart';

const { RangePicker } = DatePicker;

/**
 * 销售日期动态列表项目接口
 */
interface SaleDateDynamicsItem {
  salePrice: number; // 单买价格
  isEnable: boolean; // 开关状态
  saleDate: string; // 日期，格式为 YYYY-MM-DD
  agentPriceId?: string; // 价格 ID
  id?: string; // 主键 ID
}

/**
 * 扩展价格数据接口
 */
interface ExtendedPriceData extends API.SelfPriceStrategyItem {
  price: {
    goodsId: string;
    commissionRate: number;
    composePrice: number;
    isCompose: boolean;
    salePrice: number;
    saleDateDynamicsList?: SaleDateDynamicsItem[];
  };
}

/**
 * 价格设置弹窗属性接口
 */
export interface PriceSettingModalProps {
  visible: boolean;
  onCancel: () => void;
  selectedDate: dayjs.Moment | null;
  priceData: ExtendedPriceData;
  onSubmit?: (data: API.SelfPriceStrategyItem) => void;
  defaultTimeType?: '按星期' | '按日期';
}

/**
 * 价格设置弹窗组件
 */
const PriceSettingModal: React.FC<PriceSettingModalProps> = ({
  visible,
  onCancel,
  selectedDate,
  priceData,
  onSubmit,
  defaultTimeType = '按日期',
}) => {
  const [form] = Form.useForm();
  const [timeType, setTimeType] = useState<string>(defaultTimeType);
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().add(1, 'day'),
    dayjs().add(7, 'day'),
  ]);
  const [selectedDates, setSelectedDates] = useState<Dayjs[]>([
    dayjs('2000-01-01'),
    dayjs('2000-01-03'),
    dayjs('2000-01-05'),
  ]);
  const [marketPrice, setMarketPrice] = useState<number>(100);
  const [beginDiscount, setBeginDiscount] = useState<number>(0);
  const [endDiscount, setEndDiscount] = useState<number>(100);
  const [beginPrice, setBeginPrice] = useState<number>(0);
  const [endPrice, setEndPrice] = useState<number>(0);
  const [selectedDays, setSelectedDays] = useState<Record<string, boolean>>({
    每天: false,
    每周一: false,
    每周二: false,
    每周三: false,
    每周四: false,
    每周五: false,
    每周六: false,
    每周日: false,
  });
  // 商品信息
  const [goodsItem, setGoodsItem] = useState<any>({});
  // 添加数据加载状态
  const [dataLoaded, setDataLoaded] = useState<boolean>(false);

  // 监听弹窗关闭，重置表单验证状态
  useEffect(() => {
    if (!visible) {
      // 重置表单的所有字段，包括清除验证状态
      form.resetFields();
    }
  }, [visible, form]);

  // 组件挂载时加载缓存的商品信息
  useEffect(() => {
    if (visible) {
      const cachedItem = localStorage.getItem('priceStrategyItem');
      if (cachedItem && !dataLoaded) {
        const parsedItem = JSON.parse(cachedItem);
        setGoodsItem(parsedItem);

        // 设置市场标准价和折扣区间
        const mPrice = parsedItem.marketPrice || 100;
        const bDiscount = parsedItem.beginDiscount || 0;
        const eDiscount = parsedItem.endDiscount || 100;
        const overallDiscount = parsedItem.overallDiscount || 100;

        setMarketPrice(mPrice);
        setBeginDiscount(bDiscount);
        setEndDiscount(eDiscount);

        // 计算价格区间（与 index.tsx 口径一致：以折后单买价为基准）
        const basePrice = (overallDiscount * mPrice) / 100;
        setBeginPrice(parseFloat(((bDiscount * basePrice) / 100).toFixed(2)));
        setEndPrice(parseFloat(((eDiscount * basePrice) / 100).toFixed(2)));
      }
    }
  }, [visible, dataLoaded]);

  // 初始化时检查是否有传入的日期，如果有则切换到"按日期"模式
  useEffect(() => {
    if (visible) {
      // 使用传入的默认时间类型
      setTimeType(defaultTimeType);
      form.setFieldsValue({ timeType: defaultTimeType });

      // 如果有选中日期且是按日期模式，则设置选中日期
      if (selectedDate && defaultTimeType === '按日期') {
        // 将 dayjs 日期转换为 dayjs 日期
        const selectedDayjs = dayjs(selectedDate.format('YYYY-MM-DD'));
        setSelectedDates([selectedDayjs]);
        form.setFieldsValue({ selectedDates: [selectedDayjs] });
      }
    }
  }, [selectedDate, visible, defaultTimeType, form]);

  // 数据回显
  useEffect(() => {
    if (priceData && priceData.price) {
      // 解构取值，考虑到类型可能不完全匹配，使用类型断言
      const { salePrice, discount, saleDateDynamicsList } = priceData.price as {
        salePrice?: number;
        discount?: number;
        isCompose?: boolean;
        composePrice?: number;
        composeDiscount?: number;
        saleDateDynamicsList?: { salePrice: number; saleDate: string; isEnable: boolean }[];
      };

      // 获取市场标准价和折扣区间
      const cachedItem = localStorage.getItem('priceStrategyItem');
      if (cachedItem) {
        const parsedItem = JSON.parse(cachedItem);
        setGoodsItem(parsedItem);
        const mPrice = parsedItem.marketPrice || 100;
        const bDiscount = parsedItem.beginDiscount || 0;
        const eDiscount = parsedItem.endDiscount || 100;
        const overallDiscount = parsedItem.overallDiscount || 100;

        setMarketPrice(mPrice);
        setBeginDiscount(bDiscount);
        setEndDiscount(eDiscount);

        // 计算价格区间（与 index.tsx 口径一致：以折后单买价为基准）
        const basePrice = (overallDiscount * mPrice) / 100;
        setBeginPrice(parseFloat(((bDiscount * basePrice) / 100).toFixed(2)));
        setEndPrice(parseFloat(((eDiscount * basePrice) / 100).toFixed(2)));

        // 优先使用动态价格列表中的价格（如果存在且选中了日期）
        if (saleDateDynamicsList && saleDateDynamicsList.length > 0 && selectedDate) {
          // 查找选中日期的价格信息
          const selectedDateStr = selectedDate.format('YYYY-MM-DD');
          const dynamicItem = saleDateDynamicsList.find(
            (item) => item.saleDate === selectedDateStr,
          );
          console.log('dynamicItem=-=-=-=-', dynamicItem, saleDateDynamicsList);
          if (dynamicItem && dynamicItem.salePrice !== undefined) {
            const calculatedDiscount = Math.round((dynamicItem.salePrice / mPrice) * 100);
            form.setFieldsValue({
              priceValue: dynamicItem.salePrice,
              discountValue: calculatedDiscount,
            });
          } else {
            // 如果找不到匹配的日期价格，则置空价格输入框
            form.setFieldsValue({
              priceValue: undefined,
              discountValue: undefined,
            });
          }
        } else {
          // 如果没有动态价格列表或没有选中日期，也置空价格输入框
          form.setFieldsValue({
            priceValue: undefined,
            discountValue: undefined,
          });
        }

        // 标记数据加载完成
        setDataLoaded(true);
      }
    }
  }, [priceData, form, selectedDate]);

  // 计算选中星期对应的日期
  const calculateSelectedDates = () => {
    if (timeType !== '按星期' || !dateRange[0] || !dateRange[1]) return;

    const startDate = dateRange[0];
    const endDate = dateRange[1];
    const result: Dayjs[] = [];

    // 创建星期映射关系：中文星期名 -> 对应的星期数字 (0-6，0 表示周日)
    const weekdayMap: Record<string, number> = {
      每周日: 0,
      每周一: 1,
      每周二: 2,
      每周三: 3,
      每周四: 4,
      每周五: 5,
      每周六: 6,
    };

    // 计算从开始日期到结束日期之间的每一天
    let currentDate = startDate;
    while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
      const weekday = currentDate.day(); // 获取当前日期是星期几 (0-6)

      // 检查该星期是否被选中
      const weekdayKey = Object.keys(weekdayMap).find((key) => weekdayMap[key] === weekday);

      if (weekdayKey && (selectedDays[weekdayKey] || selectedDays['每天'])) {
        result.push(currentDate);
      }

      currentDate = currentDate.add(1, 'day');
    }

    setSelectedDates(result);
    form.setFieldsValue({ selectedDates: result });
  };

  // 当选中的星期或日期范围变化时，重新计算具体日期
  useEffect(() => {
    if (timeType === '按星期') {
      calculateSelectedDates();
    }
  }, [selectedDays, dateRange, timeType]);

  const handleDayChange = (day: string) => {
    const newSelectedDays = { ...selectedDays };

    if (day === '每天') {
      const isEveryDaySelected = !selectedDays['每天'];
      // 如果选中"每天"，则其他所有天都选中
      Object.keys(newSelectedDays).forEach((key) => {
        newSelectedDays[key] = isEveryDaySelected;
      });
    } else {
      newSelectedDays[day] = !newSelectedDays[day];

      // 检查是否所有工作日都被选中，如果是，则"每天"也选中
      const allDaysSelected = Object.keys(newSelectedDays)
        .filter((key) => key !== '每天')
        .every((key) => newSelectedDays[key]);

      newSelectedDays['每天'] = allDaysSelected;
    }

    setSelectedDays(newSelectedDays);
    form.setFieldsValue({ selectedDays: newSelectedDays });
  };

  // 处理日期范围变更
  const handleDateRangeChange = (dates: any) => {
    if (dates) {
      setDateRange(dates);
      form.setFieldsValue({ dateRange: dates });
    }
  };

  // 处理切换时间类型
  const handleTimeTypeChange = (e: any) => {
    const value = e.target.value;
    setTimeType(value);
    form.setFieldsValue({ timeType: value });
  };

  // 处理价格变化
  const handlePriceChange = (value: number | null) => {
    if (value === null) return;

    // 计算折扣百分比
    if (marketPrice > 0) {
      const discount = Math.round((value / marketPrice) * 100);
      form.setFieldsValue({ discountValue: discount });
    }
  };

  // 处理折扣变化
  const handleDiscountChange = (value: number | null) => {
    if (value === null) return;

    // 计算实际价格
    const price = parseFloat(((marketPrice * value) / 100).toFixed(2));
    form.setFieldsValue({ priceValue: price });
  };

  // 处理表单提交
  const handleFinish = (values: any) => {
    // 如果价格为空，则不提交
    if (values.priceValue === undefined || values.priceValue === null) {
      message.error('请输入单买价格');
      return;
    }

    // 价格数据
    const priceData: any = {
      salePrice: values.priceValue,
      discount: ceil(values.discountValue / 100, 4),
      isCompose: false,
      saleDateList: values.selectedDates
        ? values.selectedDates.map((date: Dayjs) => date.format('YYYY-MM-DD'))
        : timeType === '按星期'
        ? selectedDates.map((date) => date.format('YYYY-MM-DD'))
        : [],
    };

    // 调用父组件传递的 onSubmit 回调
    if (onSubmit) {
      onSubmit({
        priceId: '',
        price: priceData,
      });
    }

    // 关闭弹窗
    onCancel();
  };

  return (
    <Modal
      title="设置价格"
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={1300}
      destroyOnClose={true}
      bodyStyle={{
        padding: '16px',
        maxHeight: 'calc(100vh - 200px)',
        overflow: 'auto',
      }}
    >
      <div className="price-setting-modal-content">
        <Row gutter={24} className="price-setting-row">
          <Col span={12}>
            <div className="price-setting-container">
              {/* 替换表格为 GoodsTable 组件 */}
              <div className="goods-table-container" style={{ marginBottom: 16 }}>
                <Spin spinning={!dataLoaded}>
                  <GoodsTable goodsItem={goodsItem} beginPrice={beginPrice} endPrice={endPrice} />
                </Spin>
              </div>

              <div className="section-header" style={{ marginBottom: 16, marginTop: 16 }}>
                <div
                  className="section-title-wrapper"
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <div
                    className="section-indicator"
                    style={{
                      width: '4px',
                      height: '16px',
                      backgroundColor: '#1890ff',
                      marginRight: '8px',
                      borderRadius: '2px',
                    }}
                  />
                  <div
                    style={{
                      fontWeight: 'bold',
                      fontSize: '16px',
                    }}
                  >
                    单价设置
                  </div>
                </div>
              </div>

              <Form
                form={form}
                layout="vertical"
                onFinish={handleFinish}
                initialValues={{
                  timeType,
                  dateRange,
                  selectedDates,
                  selectedDays,
                  priceValue: undefined,
                  discountValue: undefined,
                }}
                className="price-setting-form"
              >
                <Form.Item
                  label="时间选择"
                  name="timeType"
                  rules={[{ required: true, message: '请选择时间类型' }]}
                >
                  <Radio.Group onChange={handleTimeTypeChange}>
                    <Radio value="按星期">按星期</Radio>
                    <Radio value="按日期">按日期</Radio>
                  </Radio.Group>
                </Form.Item>

                {timeType === '按星期' ? (
                  <>
                    <Form.Item
                      name="dateRange"
                      rules={[{ required: true, message: '请选择日期范围' }]}
                    >
                      <RangePicker
                        onChange={handleDateRangeChange}
                        format="YYYY-MM-DD"
                        style={{ width: '100%' }}
                        disabledDate={(current) => current && current < dayjs().startOf('day')}
                      />
                    </Form.Item>

                    <Form.Item
                      name="selectedDays"
                      rules={[
                        {
                          required: true,
                          message: '请选择周期',
                          validator: (_, value) => {
                            const hasSelectedDay = Object.keys(selectedDays)
                              .filter((key) => key !== '每天')
                              .some((key) => selectedDays[key]);

                            return hasSelectedDay
                              ? Promise.resolve()
                              : Promise.reject('请选择周期');
                          },
                        },
                      ]}
                    >
                      <div className="days-selection">
                        <div className="days-row">
                          <Checkbox
                            checked={selectedDays['每天']}
                            onChange={() => handleDayChange('每天')}
                          >
                            每天
                          </Checkbox>
                          {[
                            '每周一',
                            '每周二',
                            '每周三',
                            '每周四',
                            '每周五',
                            '每周六',
                            '每周日',
                          ].map((day) => (
                            <Checkbox
                              key={day}
                              checked={selectedDays[day]}
                              onChange={() => handleDayChange(day)}
                            >
                              {day}
                            </Checkbox>
                          ))}
                        </div>
                      </div>
                    </Form.Item>
                  </>
                ) : (
                  <Form.Item
                    name="selectedDates"
                    rules={[{ required: true, message: '请选择日期' }]}
                  >
                    <DatePicker
                      multiple
                      maxTagCount="responsive"
                      format="YYYY/MM/DD"
                      placeholder="请选择日期"
                      style={{ width: '100%' }}
                      disabledDate={(current) => current && current < dayjs().startOf('day')}
                    />
                  </Form.Item>
                )}

                <Form.Item
                  label={
                    <span>
                      单买价格
                      <Tooltip title="旺季提价增服务，淡季降价促流量，周末节假日游客需求量大，建议较工作日价格上涨10%～20%">
                        <InfoCircleOutlined style={{ marginLeft: 4 }} />
                      </Tooltip>
                    </span>
                  }
                  required
                >
                  <Space>
                    <Form.Item
                      name="priceValue"
                      noStyle
                      rules={[{ required: true, message: '请输入单买价格或市场折扣' }]}
                    >
                      <InputNumber
                        onChange={handlePriceChange}
                        style={{ width: '120px' }}
                        min={beginPrice}
                        max={endPrice || undefined}
                        precision={2}
                        addonAfter="元"
                      />
                    </Form.Item>

                    <span style={{ margin: '0 8px' }}>即市场标准价</span>

                    <Form.Item name="discountValue" noStyle>
                      <InputNumber
                        onChange={handleDiscountChange}
                        style={{ width: '100px' }}
                        min={beginDiscount}
                        max={endDiscount}
                        precision={0}
                        addonAfter="%"
                      />
                    </Form.Item>
                  </Space>
                </Form.Item>
              </Form>
            </div>
          </Col>
          <Col span={12}>
            <div className="price-history-container">
              <div className="section-header" style={{ marginBottom: 16, marginTop: 16 }}>
                <div
                  className="section-title-wrapper"
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <div
                    className="section-indicator"
                    style={{
                      width: '4px',
                      height: '16px',
                      backgroundColor: '#1890ff',
                      marginRight: '8px',
                      borderRadius: '2px',
                    }}
                  />
                  <div
                    style={{
                      fontWeight: 'bold',
                      fontSize: '16px',
                    }}
                  >
                    历史价格
                  </div>
                </div>
              </div>
              {priceData?.priceId ? (
                <PriceHistoryChart priceId={priceData.priceId} inSettingModal={true} />
              ) : (
                // <PriceHistoryChart priceId={'685074211547524796'} inSettingModal={true} />
                <div
                  style={{
                    height: '300px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Empty description="暂无价格ID，无法获取历史价格数据" />
                </div>
              )}

              <div className="section-header" style={{ marginBottom: 16, marginTop: 16 }}>
                <div
                  className="section-title-wrapper"
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <div
                    className="section-indicator"
                    style={{
                      width: '4px',
                      height: '16px',
                      backgroundColor: '#1890ff',
                      marginRight: '8px',
                      borderRadius: '2px',
                    }}
                  />
                  <div
                    style={{
                      fontWeight: 'bold',
                      fontSize: '16px',
                    }}
                  >
                    天气趋势
                  </div>
                </div>
              </div>
              <WeatherTrendChart goodsId={goodsItem.goodsId} />
              <div className="section-header" style={{ marginBottom: 16, marginTop: 16 }}>
                <div
                  className="section-title-wrapper"
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <div
                    className="section-indicator"
                    style={{
                      width: '4px',
                      height: '16px',
                      backgroundColor: '#1890ff',
                      marginRight: '8px',
                      borderRadius: '2px',
                    }}
                  />
                  <div
                    style={{
                      fontWeight: 'bold',
                      fontSize: '16px',
                    }}
                  >
                    入园情况
                  </div>
                </div>
              </div>
              <ParkEntryChart inSettingModal={true} goodsId={goodsItem.goodsId} />
            </div>
          </Col>
        </Row>
      </div>

      {/* 固定在底部的按钮 */}
      <div className="price-setting-footer">
        <Button onClick={onCancel} style={{ marginRight: 8 }}>
          取消
        </Button>
        <Button type="primary" onClick={() => form.submit()}>
          确定
        </Button>
      </div>
    </Modal>
  );
};

export default PriceSettingModal;
