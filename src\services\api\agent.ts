import { request } from '@umijs/max';
import { scenicHost } from '.';

// 虚拟导游列表
export async function getAgentList(params: any) {
  const { data } = await request(`${scenicHost}/agent/page`, {
    method: 'GET',
    params,
  });
  return data;
}

// 虚拟导游详情
export async function getAgentDetail(id: string) {
  const { data } = await request(`${scenicHost}/agent/detail?id=${id}`, {
    method: 'GET',
  });
  return data;
}

// 虚拟导游编辑
export async function editAgent(id: string, params) {
  const { data } = await request(`${scenicHost}/agent/update/${id}`, {
    method: 'PUT',
    data: params,
  });
  return data;
}

// 虚拟导游删除
export async function deleteAgent(id: string) {
  const { data } = await request(`${scenicHost}/agent/delete/${id}`, {
    method: 'DELETE',
  });
  return data;
}

// 虚拟导游新增
export async function agentCreate(params: any) {
  const { data } = await request(`${scenicHost}/agent/create`, {
    method: 'POST',
    data: params,
  });
  return data;
}
