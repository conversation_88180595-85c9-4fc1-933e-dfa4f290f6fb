import Delete from '@/common/components/Delete';
import ImageUpload from '@/common/components/FileUpload';
import ProModal from '@/common/components/ProModal';
import useModal from '@/common/components/ProModal/useProModal';
import { tableConfig } from '@/common/utils/config';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getHashParams } from '@/common/utils/tool';
import {
  agentCreate,
  deleteAgent,
  editAgent,
  getAgentDetail,
  getAgentList,
} from '@/services/api/agent';
import { PlusOutlined } from '@ant-design/icons';
import type { ProFormColumnsType } from '@ant-design/pro-components';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Image, Input, message } from 'antd';
// import Input from 'antd/es/input/Input';
import { useContext, useEffect, useRef, useState } from 'react';
import { TabKeyContext } from '../..';

export default ({ store: { value: storeId } }: any) => {
  const queryParams = getHashParams();
  // const { updateGuideInfo } = useGuide();
  const modalState = useModal();
  const actionRef = useRef<ActionType>();
  const [id, setId] = useState<string | null>();
  const coId = localStorage.getItem('currentCompanyId');

  const tabKey = useContext(TabKeyContext);

  // 新增按钮
  const addButton = (
    <Button
      key="k1"
      type="primary"
      onClick={() => {
        setId(null);
        modalState.setType('add');
      }}
    >
      <PlusOutlined /> 新增
    </Button>
  );
  const tableColumns: ProColumnType[] = [
    {
      title: '虚拟导游名称',
      dataIndex: 'name',
      render: (dom, record: any) => {
        return (
          <div
            style={{
              width: 'auto',
              maxWidth: '500px',
              whiteSpace: 'normal',
              wordWrap: 'break-word',
            }}
          >
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
              <Image
                key={record?.id}
                width={80}
                height={80}
                style={{ objectFit: 'cover', borderRadius: '4px' }}
                src={`${record?.iconUrl}`}
                preview={{ src: `${record?.iconUrl}` }}
              />
              <span style={{ fontWeight: 700 }}>{record?.name}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: '简介',
      dataIndex: 'introduction',
      search: false,
      // 修复简介文本换行问题
      render: (text: string) => (
        <div
          style={{
            minWidth: '300px',
            maxWidth: '880px', // 限制最大宽度
            whiteSpace: 'normal', // 允许正常换行
            wordBreak: 'break-word', // 长单词强制拆分换行
            lineHeight: 1.6, // 增加行高，提升可读性
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_: any, entity: any) => [
        <a
          onClick={() => {
            setId(entity.id);
            modalState.setType('edit');
          }}
          key="k2"
        >
          编辑
        </a>,
        <Delete
          key="k3"
          access={true}
          status={false}
          params={{ id: entity.id }}
          content={`删除虚拟导游“${entity.name}”后，该虚拟导游的所有应用将同时删除`}
          request={async (params) => {
            const data = await deleteAgent(params.id);
            addOperationLogRequest({
              action: 'del',
              module: tabKey,
              content: `删除【${entity.name}】虚拟导游`,
            });
            return data;
          }}
          actionRef={actionRef}
        />,
      ],
    },
  ];
  const modalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '虚拟导游名称',
          dataIndex: 'name',
          renderFormItem: () => (
            <Input showCount={true} placeholder="给我取一个名称" maxLength={20} />
          ),
          // formItemProps: {
          //   rules: [
          //     { required: true, message: '请输入虚拟导游名称' },
          //     { max: 20, message: '虚拟导游名称最多输入20字' }, // 新增长度校验
          //   ],
          // },
        },
        {
          title: '形象照片',
          dataIndex: 'iconUrl',
          formItemProps: { rules: [{ required: true, message: '请上传形象照片' }] },
          renderText: (text: any) => <ImageUpload defaultValue={text} readonly />,
          renderFormItem: (_, __, formRef) => (
            <ImageUpload defaultValue={formRef.getFieldValue('iconUrl')} size={2} maxCount={1} />
          ),
        },
        {
          title: '虚拟导游简介',
          dataIndex: 'introduction',
          formItemProps: {
            rules: [
              { required: true, message: '请描述虚拟导游简介' },
              { max: 200, message: '虚拟导游简介最多输入200字' }, // 新增长度校验
            ],
            getValueFromEvent: (e: any) => e.target.value,
          },
          renderFormItem: () => (
            <Input.TextArea
              placeholder="描述形象特征、风格，如卡通形象、人物、拟人化动物、性格、外形元素等"
              autoSize={{ minRows: 3, maxRows: 6 }}
              maxLength={200} // 限制最大输入长度
              showCount={true}
            />
          ),
        },
        {
          title: '开场白',
          dataIndex: 'openerPrompt',
          formItemProps: {
            rules: [
              { required: true, message: '请输入开场白' },
              { max: 200, message: '开场白最多输入200字' }, // 新增长度校验
            ],
            getValueFromEvent: (e: any) => e.target.value,
          },
          renderFormItem: () => (
            <Input.TextArea
              placeholder="请输入开场白内容1"
              autoSize={{ minRows: 3, maxRows: 6 }}
              maxLength={200}
              showCount={true}
            />
          ),
        },
      ],
    },
  ];

  useEffect(() => {
    if (queryParams?.openCreate === '1') {
      setId(null); // 新增模式下 id 为 null
      modalState.setType('add'); // 触发新增弹框
    }
  }, []); // 空依赖，仅挂载时执行

  return (
    <>
      <ProTable
        {...tableConfig}
        actionRef={actionRef}
        columns={tableColumns}
        toolBarRender={() => [addButton]}
        params={{ storeId, coId }}
        pagination={{
          pageSize: 10, // 强制每页显示10条（核心配置）
          // showQuickJumper: true, // 显示快速跳转页码（可选，提升效率）
        }}
        request={(params) => {
          return getAgentList(params);
        }}
      />
      <ProModal
        {...modalState}
        title="虚拟导游"
        actionRef={actionRef}
        columns={modalColumns}
        params={{ id, storeId, coId }}
        infoRequest={async (params) => {
          try {
            const res = await getAgentDetail(params.id);
            const detailData = res.data || res;
            const formattedData = {
              coId,
              name: detailData.name || detailData.agentName || '',
              iconUrl: detailData.iconUrl ? [{ fileUrl: detailData.iconUrl }] : [],
              introduction: detailData.introduction || detailData.desc || '',
              openerPrompt: detailData.openerPrompt || detailData.welcomeMsg || '',
            };
            // 返回符合ProModal期望的格式
            return { data: formattedData };
          } catch (error) {
            message.error('获取虚拟导游详情失败');
            console.error(error);
            return { data: {} };
          }
        }}
        addRequest={async (params) => {
          params.backgroundImageUrl = params.iconUrl[0].fileUrl;
          params.iconUrl = params.iconUrl[0].fileUrl;
          params.instruction = params.introduction;
          const data = await agentCreate(params);
          // 更新引导
          // updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_5 });
          addOperationLogRequest({
            action: 'add',
            module: tabKey,
            content: `新增【${params.name}】虚拟导游`,
          });
          return data;
        }}
        editRequest={async (params) => {
          // 确保传递完整的参数，包括id
          const requestData: any = {
            coId,
            name: params.name,
            backgroundImageUrl: params.iconUrl[0].fileUrl,
            iconUrl: params.iconUrl[0].fileUrl,
            instruction: params.introduction,
            introduction: params.introduction,
            openerPrompt: params.openerPrompt,
            storeId: params.storeId,
            // 根据API需要添加其他字段
          };

          const data = await editAgent(params.id, requestData);
          addOperationLogRequest({
            action: 'edit',
            module: tabKey,
            content: `编辑【${params.name}】虚拟导游`,
          });
          return data;
        }}
      />
    </>
  );
};
