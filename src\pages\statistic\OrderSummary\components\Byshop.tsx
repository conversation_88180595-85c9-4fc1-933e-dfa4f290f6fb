import DetailsPop from '@/common/components/DetailsPop';
import { tableConfig } from '@/common/utils/config';
import {
  payTypeEnum,
  productTypeEnum,
  ticketStatusEnum,
  ticketTypeEnum,
} from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { DataMaskTypeEnum } from '@/common/utils/tool';
import DataMask from '@/components/DataMask';
import { useMask } from '@/hooks/useMask';
import useModal from '@/hooks/useModal';
import OrderManagement from '@/pages/business/orderManagement/Sale/OrderManagement';
import {
  ticketDetails,
  user,
  userReceiveOrder,
  userReceiveTicket,
  userRetreatOrder,
  userRetreatTicket,
} from '@/services/api/datareport';
import { apiCoList } from '@/services/api/distribution';
import { apiStoreReturnOrderInfo } from '@/services/api/store';
import type { ActionType, ProColumns, RequestData } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Descriptions, Modal, Select, Tooltip } from 'antd';
import { uniqBy } from 'lodash';
import dayjs from 'dayjs';
import QRCode from 'qrcode.react';
import React, { useEffect, useRef, useState } from 'react';
import { useModel } from '@umijs/max';
import styles from '../index.less';
import Vessel from './Vessel';

// interface touser{ columns: any}

const Byshop: React.FC<Record<string, any>> = (props) => {
  // console.log('props', props);

  const { initialState }: any = useModel('@@initialState');

  const { coId, coName } = initialState?.currentCompany || {};
  const actionRef = useRef<ActionType>();

  // 添加数据脱敏相关 hook
  const [handleDetailsMaskChange, maskDetailsDataFn] = useMask();

  const columns: ProColumns<ActionType>[] = [
    {
      title: '店铺',
      dataIndex: 'store_name',
      key: 'store_name',
      hideInSearch: true,
    },
    {
      title: '单据类型',
      dataIndex: 'typegroup',
      key: 'type',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.typegroup.map((n: any, i: number) => {
              return <div key={i}>{n.type}</div>;
            })}
          </div>
        );
      },
    },
    {
      title: '订单数',
      dataIndex: 'typegroup',
      key: 'orderCount',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.typegroup.map((n: any, i: number) => {
              return (
                <div
                  style={{ color: '#1890ff', cursor: 'pointer' }}
                  onClick={() => {
                    circumstance(item, i, 0);
                  }}
                  key={i}
                >
                  {n.orderCount}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '票数',
      dataIndex: 'typegroup',
      key: 'ticketNum',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.typegroup.map((n: any, i: number) => {
              return (
                <div
                  style={{ color: '#1890ff', cursor: 'pointer' }}
                  onClick={() => {
                    circumstance(item, i, 1);
                  }}
                  key={i}
                >
                  {n.ticketNum}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '订单金额',
      dataIndex: 'typegroup',
      key: 'amount',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.typegroup.map((n: any, i: number) => {
              return <div key={i}>{n.amount}</div>;
            })}
          </div>
        );
      },
    },
    {
      title: '获得佣金', //（已核销佣金/待核销佣金）
      dataIndex: 'typegroup',
      key: 'commision',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={styles.user}>
            {item.typegroup.map((n: any, i: number) => {
              return <div key={i}>{n.commision}</div>;
            })}
          </div>
        );
      },
    },
    {
      title: '支付 / 退款日期',
      initialValue: [dayjs().startOf('months'), dayjs()],

      hideInForm: false,
      dataIndex: 'dates',
      key: 'dates',
      hideInSearch: false,
      valueType: 'dateRange',
      hideInTable: true,
    },
  ];

  const [index, setIndex] = useState<number>(0);
  const [store, setStore] = useState<Record<string, any>>({});
  const [co, setCo] = useState<Record<string, any>[]>([]); //景区企业

  useEffect(() => {
    if (co.length == 0)
      apiCoList({ coType: 0, settlementStatus: '1', isEnable: 1, pageSize: 99999 }).then((res) => {
        if (res.success) setCo(res.data);
      });
  }, []);

  /** 模块 */
  const [visible, setVisible] = useState<boolean>(false);

  const types = [
    { title: '订单列表', inlayTitle: '子订单' },
    { title: '出票列表', inlayTitle: '门票' },
    { title: '退单列表', inlayTitle: '退单' },
    { title: '退票列表', inlayTitle: '门票' },
  ];

  //弹框
  const circumstance = (item: any, type: number, i: number) => {
    // console.log('circumstance', item, type, i);
    setStore(item);
    if (type == 0) setIndex(type + i);
    if (type == 1) setIndex(type + i + 1);
    setVisible(true);
  };

  const [amount, setAmount] = useState<Record<string, any>[]>([]); //合计
  const [total, setTotal] = useState<number>(0); //
  const [dates, setDates] = useState<Record<string, any>[] | null>(null); //总表时间

  //经销商选择器
  const distributorSelect = (config: any) => {
    return (
      <Select
        {...config}
        // mode="multiple"
        allowClear
        style={{ width: '100%' }}
        placeholder="请选择"
        defaultValue={[]}
        options={uniqBy(
          [
            //取本企业有关经销商插入
            ...props.supplierList.map((n: any) => ({
              label: n.coName,
              value: n.coId,
            })),
            //把本企业也当成经销商插入
            {
              label: coName,
              value: coId,
            },
          ],
          'value',
        )}
      />
    );
  };

  const childColumns: ProColumns<ActionType>[][] = [
    [
      //订单
      {
        title: '子订单号',
        dataIndex: 'id',
        key: 'id',
        hideInSearch: false,
        render: (_, item: any) => {
          return (
            <div style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => details(item)}>
              {item.id}
            </div>
          );
        },
      },
      {
        title: '服务商',
        dataIndex: 'service_provider_name',
        key: 'service_provider_name',
        hideInSearch: false,
        renderFormItem: (config) => {
          return (
            <Select
              {...config}
              // mode="multiple"
              allowClear
              style={{ width: '100%' }}
              placeholder="请选择"
              defaultValue={[]}
            >
              {co.map((n: any) => {
                return <Select.Option key={n.coId}>{n.coName}</Select.Option>;
              })}
            </Select>
          );
        },
      },
      {
        title: '人数',
        dataIndex: 'nums',
        key: 'nums',
        hideInSearch: true,
      },
      {
        title: '支付金额',
        dataIndex: 'amounts',
        key: 'amounts',
        hideInSearch: true,
      },
      {
        title: '经销商',
        dataIndex: 'distributor_name',
        key: 'distributor_name',
        hideInSearch: false,
        renderFormItem: (config) => distributorSelect(config),
      },
      {
        title: '佣金',
        dataIndex: 'commision',
        key: 'commision',
        hideInSearch: true,
      },
      {
        title: '支付方式',
        dataIndex: 'paytype',
        key: 'paytype',
        hideInSearch: false,
        initialValue: '',
        valueEnum: payTypeEnum,
      },
      {
        title: '下单时间',
        dataIndex: 'createtime',
        key: 'createtime',
        hideInSearch: false,
        valueType: 'dateRange',
        render: (_, item: any) => <>{item.createtime?.split('.')[0]}</>,
      },
      {
        title: '支付时间',
        dataIndex: 'paytime',
        key: 'paytime',
        hideInSearch: false,
        valueType: 'dateRange',
        initialValue: dates,
        render: (_, item: any) => <>{item.paytime?.split('.')[0]}</>,
      },
    ],
    [
      //出票
      {
        title: '票号',
        key: 'ticket_number',
        dataIndex: 'ticket_number',
        hideInSearch: false,
        render: (_: any, item: any) => {
          if (!item.ticket_number) {
            return '-';
          }
          const ticket_number = item.ticket_number?.replace(
            item.ticket_number.substring(2, item.ticket_number.length - 4),
            '****',
          );
          return (
            <div style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => details(item)}>
              <Tooltip title={item.ticket_number}>{ticket_number}</Tooltip>
            </div>
          );
        },
      },
      {
        title: '服务商',
        key: 'service_provider_name',
        dataIndex: 'service_provider_name',
        hideInSearch: false,
        renderFormItem: (config) => {
          return (
            <Select
              {...config}
              // mode="multiple"
              allowClear
              style={{ width: '100%' }}
              placeholder="请选择"
              defaultValue={[]}
            >
              {co.map((n: any) => {
                return <Select.Option key={n.coId}>{n.coName}</Select.Option>;
              })}
            </Select>
          );
        },
      },
      {
        title: '商品名称',
        key: 'product_name',
        dataIndex: 'product_name',
        hideInSearch: false,
      },
      {
        title: '票种',
        key: 'ticket_type',
        dataIndex: 'ticket_type',
        hideInSearch: false,
        valueEnum: ticketTypeEnum,
        render: (_, item: any) => <>{ticketTypeEnum[item.ticket_type]}</>,
      },
      {
        title: '分时预约',
        key: 'time_share',
        dataIndex: 'time_share',
        hideInSearch: true,
      },
      {
        title: '入园时间',
        key: 'day',
        dataIndex: 'day',
        hideInSearch: false,
        valueType: 'dateRange',
        render: (_, item: any) => <>{item.day?.split('.')[0]}</>,
      },
      {
        title: '经销商',
        key: 'distributor_name',
        dataIndex: 'distributor_name',
        hideInSearch: false,
        renderFormItem: (config) => distributorSelect(config),
      },
      {
        title: '售价',
        key: 'product_price',
        dataIndex: 'product_price',
        hideInSearch: true,
      },
      {
        title: '佣金',
        key: 'actual_com_amount',
        dataIndex: 'actual_com_amount',
        hideInSearch: true,
      },
      {
        title: '支付时间',
        key: 'pay_time',
        dataIndex: 'pay_time',
        valueType: 'dateRange',
        initialValue: dates,
        render: (_, item: any) => <>{item.pay_time?.split('.')[0]}</>,
      },
      {
        title: '订单号',
        key: 'order_id',
        dataIndex: 'order_id',
        hideInSearch: false,
      },
    ],
    [
      //退单
      {
        title: '退单号',
        key: 'refund_id',
        dataIndex: 'refund_id',
        hideInSearch: false,
        render: (_, item: any) => {
          return (
            <div style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => details(item)}>
              {item.refund_id}
            </div>
          );
        },
      },
      {
        title: '子订单号',
        key: 'order_id',
        dataIndex: 'order_id',
        hideInSearch: false,
      },
      {
        title: '服务商',
        key: 'service_provider_name',
        dataIndex: 'service_provider_name',
        hideInSearch: false,
        renderFormItem: (config) => {
          return (
            <Select
              {...config}
              // mode="multiple"
              allowClear
              style={{ width: '100%' }}
              placeholder="请选择"
              defaultValue={[]}
            >
              {co.map((n: any) => {
                return <Select.Option key={n.coId}>{n.coName}</Select.Option>;
              })}
            </Select>
          );
        },
      },
      {
        title: '经销商',
        key: 'distributor_name',
        dataIndex: 'distributor_name',
        hideInSearch: false,
        renderFormItem: (config) => distributorSelect(config),
      },
      {
        title: '退票数',
        key: 'numbers',
        dataIndex: 'numbers',
        hideInSearch: true,
      },
      {
        title: '退款金额',
        key: 'amounts',
        dataIndex: 'amounts',
        hideInSearch: true,
      },
      {
        title: '退佣金额',
        key: 'commision',
        dataIndex: 'commision',
        hideInSearch: true,
      },
      {
        title: '手续费',
        key: 'refund_fee',
        dataIndex: 'refund_fee',
        hideInSearch: true,
      },
      {
        title: '创建时间',
        key: 'create_time',
        dataIndex: 'create_time',
        hideInSearch: false,
        valueType: 'dateRange',
        render: (_, item: any) => <>{item.create_time?.split('.')[0]}</>,
      },
      {
        title: '退款时间',
        key: 'refund_time',
        dataIndex: 'refund_time',
        hideInSearch: false,
        valueType: 'dateRange',
        initialValue: dates,
        render: (_, item: any) => <>{item.refund_time?.split('.')[0]}</>,
      },
    ],
    [
      //退票
      {
        title: '票号',
        key: 'ticket_number',
        dataIndex: 'ticket_number',
        hideInSearch: false,
        render: (_: any, item: any) => {
          if (!item.ticket_number) {
            return '-';
          }
          const ticket_number = item.ticket_number?.replace(
            item.ticket_number.substring(2, item.ticket_number.length - 4),
            '****',
          );
          return (
            <div style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => details(item)}>
              <Tooltip title={item.ticket_number}>{ticket_number}</Tooltip>
            </div>
          );
        },
      },
      {
        title: '服务商',
        key: 'service_provider_name',
        dataIndex: 'service_provider_name',
        hideInSearch: false,
        renderFormItem: (config) => {
          return (
            <Select
              {...config}
              // mode="multiple"
              allowClear
              style={{ width: '100%' }}
              placeholder="请选择"
              defaultValue={[]}
            >
              {co.map((n: any) => (
                <Select.Option key={n.coId}>{n.coName}</Select.Option>
              ))}
            </Select>
          );
        },
      },
      {
        title: '商品名称',
        key: 'product_name',
        dataIndex: 'product_name',
        hideInSearch: false,
      },
      {
        title: '票种',
        key: 'ticket_type',
        dataIndex: 'ticket_type',
        hideInSearch: false,
        valueEnum: ticketTypeEnum,
        // render: (_, item: any) => <>{ticketTypeEnum[item.ticket_type]}</>,
      },
      {
        title: '分时预约',
        key: 'time_share',
        dataIndex: 'time_share',
        hideInSearch: true,
      },
      {
        title: '入园时间',
        key: 'day',
        dataIndex: 'day',
        hideInSearch: true,
        render: (_, item: any) => <>{item.day?.split('.')[0]}</>,
      },
      {
        title: '经销商',
        key: 'distributor_name',
        dataIndex: 'distributor_name',
        hideInSearch: false,
        renderFormItem: (config) => distributorSelect(config),
      },
      {
        title: '售价',
        key: 'refund_amount',
        dataIndex: 'refund_amount',
        hideInSearch: true,
      },
      {
        title: '佣金',
        key: 'actual_com_amount',
        dataIndex: 'actual_com_amount',
        hideInSearch: true,
      },
      {
        title: '退款时间',
        key: 'refund_time',
        dataIndex: 'refund_time',
        hideInSearch: false,
        valueType: 'dateRange',
        initialValue: dates,
        render: (_, item: any) => <>{item.refund_time?.split('.')[0]}</>,
      },
      {
        title: '退单号',
        key: 'refund_id',
        dataIndex: 'refund_id',
        hideInSearch: false,
      },
    ],
  ];

  const request =
    (fn: any, type: string) =>
    async (q: Record<string, any>): Promise<Partial<RequestData<ActionType>>> => {
      // console.log('q', q);

      const params: any = {};
      if (type == 'receiveOrder') {
        params.order_id = q.id ? q.id : null;
        params.pay_type = q.paytype ? q.paytype : null;
        params.start_year_month_day_pay = q.paytime ? q.paytime[0] : null;
        params.end_year_month_day_pay = q.paytime ? q.paytime[1] : null;
      } else if (type == 'retreatOrder') {
        params.order_id = q.order_id ? q.order_id : null;
        params.start_year_month_day_pay = q.refund_time ? q.refund_time[0] : null;
        params.end_year_month_day_pay = q.refund_time ? q.refund_time[1] : null;
      } else if (type == 'receiveTicket') {
        params.order_id = q.order_id ? q.order_id : null;
        params.start_year_month_day = q.day ? q.day[0] : null;
        params.end_year_month_day = q.day ? q.day[1] : null;
        params.start_year_month_day_pay = q.pay_time ? q.pay_time[0] : null;
        params.end_year_month_day_pay = q.pay_time ? q.pay_time[1] : null;
      } else if (type == 'retreatTicket') {
        params.start_year_month_day_pay = q.refund_time ? q.refund_time[0] : null;
        params.end_year_month_day_pay = q.refund_time ? q.refund_time[1] : null;
      }
      const obj = {
        conf: {
          store_id: store.store_id + '',
          service_provider_id: q.service_provider_name ? q.service_provider_name : null,
          distributor_id: q.distributor_name ? q.distributor_name : null,

          start_year_month_day: q.createtime ? q.createtime[0] : null,
          end_year_month_day: q.createtime ? q.createtime[1] : null,
          m: q.pageSize, //条数
          n: q.current, //页码
          agent_id: initialState.currentCompany.coId,

          ticket_type: q.ticket_type ? q.ticket_type : null,
          ticket_number: q.ticket_number ? q.ticket_number : null,
          product_name: q.product_name ? q.product_name : null,
          refund_id: q.refund_id ? q.refund_id : null,

          ...params,
        },
      };

      const data: any = [];
      try {
        const { result } = await fn(obj);
        setAmount([]);
        setTotal(0);
        result.map((_n: any, i: any) => {
          if (_n.id == '合计' || _n.refund_id == '合计') {
            let amount: Record<string, any>[] = [];
            if (type == 'receiveOrder') {
              amount = [
                { label: '支付票数：', value: _n.nums },
                { label: '支付金额：', value: `${(_n.amounts * 1).toFixed(2)}` },
                { label: '佣金：', value: `${(_n.commision * 1).toFixed(2)}` },
              ];
            } else if (type == 'retreatOrder') {
              amount = [
                { label: '退票数：', value: _n.numbers },
                { label: '退款金额：', value: `${(_n.amounts * 1).toFixed(2)}` },
                { label: '退佣金额：', value: `${(_n.commision * 1).toFixed(2)}` },
                { label: '手续费：', value: `${(_n.refund_fee * 1).toFixed(2)}` },
              ];
            }
            setAmount(amount);
          } else {
            // _n.paytime=formatTime(_n.paytime)
            data.push(_n);
          }
          if (_n.total != 0) {
            setTotal(_n.total);
          }
        });
      } catch (err) {
        console.log(err);
      }
      return await new Promise((resolve, reject) => resolve({ data, total: total }));
    };

  const requests: any[] = [
    request(userReceiveOrder, 'receiveOrder'),
    request(userReceiveTicket, 'receiveTicket'),
    request(userRetreatOrder, 'retreatOrder'),
    request(userRetreatTicket, 'retreatTicket'),
  ];

  /** 详情模块 */
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);
  const [orderVisible, setOrderVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const [detailsWidth, setDetailsWidth] = useState<number>(modelWidth.md);
  const [dataSource, setDataSource] = useState<Record<string, any>>({}); //退单详情
  const [rowInfo, setRowInfo] = useState<Record<string, any>>({});

  // 订单状态
  const orderModal = useModal();
  const [currentRow, setCurrentRow] = useState<Record<string, any>>();

  const details = async (item: any) => {
    // console.log(item);
    if (index % 2 == 0) {
      //订单详情
      try {
        if (index == 2) {
          setLoading(true);
          setOrderVisible(true);
          const { data } = await apiStoreReturnOrderInfo({ id: item.refund_id });
          setDataSource(data);
          setLoading(false);
        } else {
          // 抽出订单详情的业务逻辑
          setCurrentRow({ ...item, orderId: item.id });
          orderModal.setVisible(true);
        }
      } catch (e) {
        console.error(e);
        return {};
      }
    } else {
      //门票详情
      try {
        const { code, data } = await ticketDetails(item.ticket_number);
        if (code == 20000) {
          setRowInfo(data);
        }
        setDetailsVisible(true);
      } catch (err) {
        console.log(err);
      }
    }
  };

  return (
    <>
      <ProTable<ActionType, ProColumns>
        {...tableConfig}
        bordered
        actionRef={actionRef}
        rowKey="dataIndex"
        // options={{ setting: false, density: false }}
        // search={{ labelWidth: 'auto' }}
        request={async (params: Record<string, any>): Promise<Partial<RequestData<ActionType>>> => {
          // console.log(params);
          const obj = {
            conf: {
              agent_id: initialState.currentCompany.coId,
              store_id: null,
              start_year_month_day_pay: params.dates ? params.dates[0] : null,
              end_year_month_day_pay: params.dates ? params.dates[1] : null,
            },
          };
          setDates(params.dates ? params.dates : null);
          const data: any = [];
          try {
            const { result } = await user(obj);
            addOperationLogRequest({
              action: 'info',
              content: `查看面向用户订单 - 按店铺汇总`,
            });
            const store_ids: string[] = [];
            result.map((n: any) => {
              // n.store_id =props.showList[props.showList.findIndex((e: { id: any }) => e.id == n.store_id) ].id; //失去精度数字与原字符串比较 字符串->数字再比较
              if (store_ids.includes(n.store_id)) {
                data[
                  data.findIndex((e: { store_id: any }) => e.store_id == n.store_id)
                ].typegroup.push(n);
              } else {
                store_ids.push(n.store_id);

                data.push({
                  store_id: n.store_id,
                  store_name:
                    props.showList[props.showList.findIndex((e: { id: any }) => e.id == n.store_id)]
                      .name,
                  typegroup: [n],
                });
              }
            });
            data.map((l: any) => {
              if (l.typegroup.length < 2) {
                l.typegroup.push({
                  amount: 0.0,
                  commision: 0.0,
                  orderCount: 0,
                  ticketNum: 0,
                  type: '退单',
                });
              }
            });
          } catch (err) {
            console.log(err);
          }

          return await new Promise((resolve, reject) => resolve({ data }));
        }}
        columns={columns}
        pagination={{
          showQuickJumper: true,
          // hideOnSinglePage: true,
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
        }}
        editable={{
          type: 'multiple',
        }}
      />

      {/* 弹窗 */}
      <Vessel
        type={types[index]?.title}
        width={modelWidth.xl}
        total={total}
        visible={visible}
        setVisible={setVisible}
        columns={childColumns[index]}
        request={requests[index]}
        amount={amount}
        head={{ name: '店铺', value: store.store_name }}
        setAmount={setAmount}
      />

      {/* 细节弹窗  */}
      <Modal
        title={
          <>
            <span>{`${types[index]?.inlayTitle}详情`}</span>
            <DataMask
              onDataMaskChange={handleDetailsMaskChange}
              logContent="查看【门票详情】用户隐私信息"
            />
          </>
        }
        visible={detailsVisible}
        width={detailsWidth}
        destroyOnClose={true}
        footer={
          <>
            <Button onClick={() => setDetailsVisible(false)}>取消</Button>
          </>
        }
        onCancel={async () => {
          setDetailsVisible(false);
        }}
      >
        {/* 门票详情 */}
        <Descriptions title="基础信息" column={2}>
          <Descriptions.Item label="票号">{rowInfo.id}</Descriptions.Item>
          <Descriptions.Item label="服务商名称">{rowInfo.providerName}</Descriptions.Item>
          <Descriptions.Item label="产品名称">{rowInfo.proName}</Descriptions.Item>
          <Descriptions.Item label="产品类型">{productTypeEnum[rowInfo.proType]}</Descriptions.Item>
          <Descriptions.Item label="商品名称">{rowInfo.goodsName}</Descriptions.Item>
          <Descriptions.Item label="票种">{ticketTypeEnum[rowInfo.goodsType]}</Descriptions.Item>
          <Descriptions.Item label="购票人姓名">
            {maskDetailsDataFn(rowInfo.buyerName, DataMaskTypeEnum.NAME)}
          </Descriptions.Item>
          <Descriptions.Item label="购票人身份证号">
            {maskDetailsDataFn(rowInfo.buyerId, DataMaskTypeEnum.ID_CARD)}
          </Descriptions.Item>
          <Descriptions.Item label="入园时间">{rowInfo.enterDate}</Descriptions.Item>
          <Descriptions.Item label="状态">{ticketStatusEnum[rowInfo.status]}</Descriptions.Item>
          <Descriptions.Item label="出票时间">{rowInfo.createTime}</Descriptions.Item>
          <Descriptions.Item label="订单号">{rowInfo.orderId}</Descriptions.Item>
          <Descriptions.Item label="二维码">
            <QRCode
              style={{ margin: '0px 0px', position: 'relative' }}
              value={rowInfo.printStr} //value参数为生成二维码的链接 我这里是由后端返回
              size={120} //二维码的宽高尺寸
              fgColor="#000000" //二维码的颜色
            />
          </Descriptions.Item>
          <Descriptions.Item label="景区名称">{rowInfo.scenicName}</Descriptions.Item>
        </Descriptions>
      </Modal>

      {/* 退单详情 */}
      <DetailsPop
        width={modelWidth.lg}
        title="退单详情"
        visible={orderVisible}
        isLoading={loading}
        setVisible={setOrderVisible}
        columnsInitial={props.columnsDetail}
        dataSource={dataSource}
        zIndex={1001}
      />
      {/* 订单详情 */}
      <OrderManagement modalState={orderModal} currentRow={currentRow} />
    </>
  );
};

export default Byshop;
