import ProModal from '@/common/components/ProModal';
import useProModal from '@/common/components/ProModal/useProModal';
import TimeStore from '@/common/components/TimeStore';
import { tableConfig } from '@/common/utils/config';
import { productTypeEnum, ticketTypeEnum } from '@/common/utils/enum';
import { transformTimeText } from '@/common/utils/toolText';
import useModal from '@/hooks/useModal';
import {
  getDistributorStockTotalInfo,
  getStockGoodsDetailsList,
  getStockProductList,
  getUnifyPullDownStock,
} from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import type { ProFormColumnsType } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useLocation } from '@umijs/max';
import { Space, Table, Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useModel, useRequest } from '@umijs/max';
import DetailModal from './components/DetailModal';
import ExpandedContent from './components/ExpandedContent';

const TableList: React.FC = () => {
  // 分时库存
  const timeModalState = useProModal();
  const [ticketGoodsRow, setTicketGoodsRow] = useState<any>({});
  const [lawsList, setLawsList] = useState<any>([]);
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const goodsName = searchParams.get('goodsName');
  const lawsColumns = [
    {
      title: ({ beginTime, endTime }: any) => `分时时段：${[beginTime, endTime].join('-')}`,
      render: () => {},
    },
    {
      title: () => '可用库存/总库存量',
      render: ({ availableNumber = 0, stockAmount = 0 }) => `${availableNumber}/${stockAmount}`,
    },
  ];
  const timeModalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '入园有效时间',
          valueType: 'dateRange',
          dataIndex: 'dateRange',
          initialValue: [ticketGoodsRow.enterStartTime, ticketGoodsRow.enterEndTime],
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '',
          dataIndex: 'timeLaws',
          colProps: { span: 24 },
          renderFormItem: () => (
            <TimeStore
              lawsColumns={lawsColumns}
              lawsList={lawsList}
              get={(timeShare: any, timeShareData: any) => {
                setTimeShareTime(
                  timeShareData + ' ' + [timeShare.beginTime, timeShare.endTime].join('-'),
                );
                setTimeDetailsData({
                  distributorId: coId,
                  ticketGoodsId: ticketGoodsRow.goodsId,
                  timeShareData,
                  timeShareId: timeShare.id,
                });
                timeDetailsModalState.setType('info');
              }}
            />
          ),
        },
        {
          title: '可用库存',
          dataIndex: 'availableNumberSum',
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '总库存量',
          dataIndex: 'stockAmountSum',
          fieldProps: {
            disabled: true,
          },
        },
      ],
    },
  ];

  // 分时库存详情
  const timeDetailsModalState = useProModal();
  const [timeShareTime, setTimeShareTime] = useState<any>();
  const [timeDetailsData, setTimeDetailsData] = useState<any>({});
  const timeModalDetailsColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '',
          dataIndex: 'table',
          colProps: { span: 24 },
          render: (dom: any) => (
            <Table
              style={{
                width: '100%',
              }}
              pagination={false}
              dataSource={dom}
              columns={[
                {
                  title: '库存批次号',
                  dataIndex: 'batchId',
                  render: (dom, record) => (
                    <Space>
                      {dom}
                      {record.isExchange === 1 && <Tag color="blue">交易所</Tag>}
                    </Space>
                  ),
                },
                {
                  title: '可用库存',
                  dataIndex: 'availableNumber',
                },
                {
                  title: '总库存量',
                  dataIndex: 'stockAmount',
                },
              ]}
            />
          ),
        },
      ],
    },
  ];

  const { initialState } = useModel('@@initialState');
  const { coId } = initialState?.currentCompany || {};

  const actionRef = React.useRef<any>();
  const detailModal = useModal();
  const [currentRow, setCurrentRow] = useState<API.TotalStockItem>();

  const getSenicListReq = useRequest(getUnifyPullDownStock, {
    manual: true,
    initialData: [],
    formatResult: (res) => {
      return Object.keys(res.data).map((key: string) => {
        return {
          value: key,
          label: res.data[key],
        };
      });
    },
  });
  const getSupplierListReq = useRequest(getUnifyPullDownStock, {
    manual: true,
    initialData: [],
    formatResult: (res) => {
      return Object.keys(res.data).map((key: string) => {
        return {
          value: key,
          label: res.data[key],
        };
      });
    },
  });
  const getProductListReq = useRequest(getUnifyPullDownStock, {
    manual: true,
    initialData: [],
    formatResult: (res) => {
      return Object.keys(res.data).map((key: string) => {
        return {
          value: res.data[key],
          label: res.data[key],
        };
      });
    },
  });

  useEffect(() => {
    if (goodsName && actionRef.current) {
      actionRef.current?.setFieldsValue({ goodsName });
    }
  }, [goodsName]);

  useEffect(() => {
    if (coId) {
      getSenicListReq.run({ getUnifyPullDown: coId, type: '1' });
      getSupplierListReq.run({ getUnifyPullDown: coId, type: '2' });
      getProductListReq.run({ getUnifyPullDown: coId, type: '3' });
    }
  }, [coId]);

  const columns: ProColumns<API.TotalStockItem>[] = [
    {
      title: '景区名称',
      dataIndex: 'scenicName',
      hideInSearch: true,
    },
    {
      title: '景区名称',
      dataIndex: 'scenicId',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getSenicListReq.data,
        showSearch: true,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      valueType: 'select',
      fieldProps: {
        options: getProductListReq.data,
        showSearch: true,
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierId',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: getSupplierListReq.data,
        showSearch: true,
      },
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierNames',
      hideInSearch: true,
    },
    {
      title: '产品类型',
      dataIndex: 'proType',
      hideInSearch: true,
      valueEnum: productTypeEnum,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '票种',
      hideInSearch: true,
      dataIndex: 'ticketGoodsType',
      valueEnum: ticketTypeEnum,
    },
    {
      title: '购买有效时间',
      dataIndex: 'createTime',
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          return {
            purchaseBeginTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            purchaseEndTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },
    {
      title: '购买有效时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      render: (dom: any, entity) => {
        return transformTimeText(entity.purchaseBeginTime, entity.purchaseEndTime);
      },
    },
    {
      title: '入园有效时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      render: (dom: any, entity) => {
        return transformTimeText(entity.enterStartTime, entity.enterEndTime);
      },
    },
    {
      title: '入园有效时间',
      dataIndex: 'enterStartTime',
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          return {
            enterStartTime: dayjs(value[0], 'YYYY-MM-DD').format('YYYY-MM-DD'),
            enterEndTime: dayjs(value[1], 'YYYY-MM-DD').format('YYYY-MM-DD'),
          };
        },
      },
    },

    // {
    //   title: '入园日期',
    //   dataIndex: 'enterStartTime',
    //   hideInSearch: true,
    //   render: (dom, entity) => {
    //     const { timeRestrict, categoryType } = entity;
    //     // 分时票或者权益卡
    //     if (timeRestrict === '0' || categoryType === 2) {
    //       return (
    //         <a
    //           onClick={() => {
    //             setCurrentRow(entity);
    //             detailModal.setVisible(true);
    //           }}
    //         >
    //           详情
    //         </a>
    //       );
    //     }
    //     return '-';
    //   },
    // },

    {
      title: '总库存量',
      dataIndex: 'totalNumber',
      hideInSearch: true,
    },
    {
      title: '可用库存',
      dataIndex: 'availableNumber',
      hideInSearch: true,
    },
    {
      title: '已售库存',
      dataIndex: 'spendNumber',
      hideInSearch: true,
    },
    {
      title: '冻结库存（贷款）',
      dataIndex: 'newFreezeNum',
      hideInSearch: true,
    },
    {
      title: '冻结库存（挂单）',
      dataIndex: 'positionFreezeNum',
      hideInSearch: true,
    },
    {
      title: '过期库存',
      dataIndex: 'expiredStock',
      hideInSearch: true,
    },
    {
      title: '销毁库存',
      dataIndex: 'destroyInventoryNum',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      fixed: 'right',
      hideInSearch: true,
      render: (dom, entity: any) => (
        <Space>
          <a
            onClick={() => {
              // 子列表添加总结列
              const total = {
                ...entity,
                batchId: '总计',
                colType: 'total',
              };
              const hasTotalItem = entity.stockList.some((item) => item.colType === 'total');
              if (!hasTotalItem) entity.stockList.push(total);

              setCurrentRow(entity);
              detailModal.setVisible(true);
            }}
          >
            查看
          </a>
          {entity?.stockList?.[0]?.timeShareId ? (
            <a
              onClick={() => {
                setTicketGoodsRow(entity);
                timeModalState.setType('edit');
              }}
            >
              分时详情
            </a>
          ) : null}
        </Space>
      ),
    },
  ];

  const tableRequest = async (params: any) => {
    const formValues: any = actionRef.current?.getFieldsValue();
    const { data } = await getStockProductList({ ...params, goodsName: formValues?.goodsName });
    return data;
  };

  return (
    <>
      <ProTable<API.TotalStockItem>
        {...tableConfig}
        formRef={actionRef}
        rowKey="goodsId"
        pagination={{
          defaultPageSize: 10,
        }}
        params={{ distributorId: coId, goodsName }}
        request={tableRequest}
        columns={columns}
        expandable={{
          expandedRowRender: (record, index, indent, expanded) => (
            <ExpandedContent dataItem={record} expanded={expanded} />
          ),
          rowExpandable: (record) => record.timeRestrict === '1',
        }}
      />

      <DetailModal modalState={detailModal} isTimeShare={false} dataItem={currentRow} />
      {/* 分时库存 */}
      <ProModal
        {...timeModalState}
        fullTitle="采购数量"
        columns={timeModalColumns}
        layout="horizontal"
        infoRequest={async () => {
          const { data }: any = await getDistributorStockTotalInfo({
            ticketGoodsId: ticketGoodsRow.goodsId,
            distributorId: coId,
          });
          setLawsList(data.timeShareVoList);
          return { data };
        }}
        onFinish={async () => true}
      />
      {/* 分时库存详情 */}
      <ProModal
        {...timeDetailsModalState}
        fullTitle={timeShareTime}
        columns={timeModalDetailsColumns}
        infoRequest={async () => {
          const { data }: any = await getStockGoodsDetailsList(timeDetailsData);
          return { data: { table: data } };
        }}
      />
    </>
  );
};

export default TableList;
