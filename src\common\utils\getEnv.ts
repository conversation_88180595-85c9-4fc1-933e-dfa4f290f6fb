const envMap = {
  local: {
    API_HOST: '/api', // 接口地址
    UPLOAD_HOST: '/upload', // 图片上传地址
    IMG_HOST: '/previewImg', // 图片预览地址
    CHART_HOST: '/chartApi', // 首页图表
    DATA_HOST: '/chartApi', // data 数据报表
    OPERATION_HOST: '/operationApi', // 操作日志接口
    BIG_DATA_HOST: '/bigDataApi', // 大数据接口地址
    AI_HOST: '/aiApi', // AI 接口
  },
  dev: {
    ENV: 'dev', // 运行环境
    API_HOST: 'https://dev.shukeyun.com/common/common-gateway', // 接口地址
    BIG_DATA_HOST: 'https://dev.shukeyun.com/data/api', // 大数据接口地址
    LOGIN_HOST: 'https://dev.shukeyun.com/cas/login', // 登录页地址
    CAS_HOST: 'https://dev.shukeyun.com/cas/web', // CAS 地址
    UPLOAD_HOST: 'https://test.shukeyun.com/maintenance/deepfile/', // 图片上传地址
    IMG_HOST: 'https://test.shukeyun.com/maintenance/deepfile/', // 图片预览地址
    IMG_HOSTCOMMENT: 'https://test.shukeyun.com/maintenance/deepfile/', // 图片预览地址
    SCENIC_HOST: 'https://dev.shukeyun.com/scenic/backend-v2/', // 景区地址
    MERCHANT_HOST: 'https://dev.shukeyun.com/upaypal/hp', // 结算商户中心地址
    FINANCIAL_HOST: 'https://dev.shukeyun.com/cas/login/#/login?appId=3h6pTlCErGAcxs5XO3jk', // 金融系统地址
    PAY_HOST: 'https://dev.shukeyun.com/upaypal/cashier/#/counter', // 结算支付地址
    MALL_HOST: 'https://dev.shukeyun.com/scenic/shop/#/', // h5 地址
    HOUSE_URL: 'https://dev.shukeyun.com/scenic/house/', // 售票窗口地址
    OPERATION_HOST: 'https://dev.shukeyun.com/data/sensor/db', // 操作日志接口
    NFT_HOST: 'https://dev.shukeyun.com/blockchain/nft-ui/#/DigitalCollection', // NFT 浏览器
    APPID: '0cBALgKikW8FgZD0NR6y', // cas 登录 appid
    DEFAULT_LOGO: 'data/2022-12-08/upload_a1c14b0ddec37ec158a04a1685decac0.png', // 易旅通默认 logo 半路径
    DATA_HOST: 'https://dev.shukeyun.com/data/api/model/exec', // DATA 接口地址
    CHART_HOST: 'https://test.shukeyun.com/data/api/model/exec', // 首页报表接口地址
    HELP_URL: 'https://dev.shukeyun.com/scenic/help-frontend', // 帮助中心
    FIT_URL: 'https://dev.shukeyun.com/scenic/interface-designer-frontend/#/home', // 店铺装修
    FILE_HOST: 'https://minio-prod.shukeyun.com/scenic-test/', // 文件查看地址
    CHAIN_URL: 'https://dev-explorer.shukechain.com', // 智旅链浏览器
    AI_HOST: 'https://dev-gcluster.shukeyun.com', // ai 接口地址
    BACKEND_V2: 'https://dev.shukeyun.com/scenic/backend-v2/#/', // 跳转景区
  },
  test: {
    ENV: 'test', // 运行环境
    BIG_DATA_HOST: 'https://test.shukeyun.com/data/api', // 大数据接口地址
    API_HOST: 'https://test.shukeyun.com/common/common-gateway', // 接口地址
    LOGIN_HOST: 'https://test.shukeyun.com/cas/login', // 登录页地址
    CAS_HOST: 'https://test.shukeyun.com/cas/web', // CAS 地址
    UPLOAD_HOST: 'https://test.shukeyun.com/maintenance/deepfile/', // 图片上传地址
    IMG_HOST: 'https://test.shukeyun.com/maintenance/deepfile/', // 图片预览地址
    IMG_HOSTCOMMENT: 'https://test.shukeyun.com/maintenance/deepfile/', // 图片预览地址
    SCENIC_HOST: 'https://test.shukeyun.com/scenic/backend-v2/', // 景区地址
    MERCHANT_HOST: 'https://test.shukeyun.com/upaypal/hp', // 结算商户中心地址
    FINANCIAL_HOST: 'https://test.shukeyun.com/cas/login/#/login?appId=DCt0iadiBQDap9yAZeHs', // 金融系统地址
    PAY_HOST: 'https://test.shukeyun.com/upaypal/cashier/#/counter', // 结算支付地址
    MALL_HOST: 'https://test.shukeyun.com/scenic/shop/#/', // h5 地址
    HOUSE_URL: 'https://test.shukeyun.com/scenic/house/', // 售票窗口地址
    OPERATION_HOST: 'https://test.shukeyun.com/data/sensor/db', // 操作日志接口
    NFT_HOST: 'https://test.shukeyun.com/blockchain/nft-ui/#/DigitalCollection', // NFT 浏览器
    APPID: 'lo1fF7roXrrdoKX7lrCZ', // cas 登录 appid
    DEFAULT_LOGO: 'data/2022-12-08/upload_a1c14b0ddec37ec158a04a1685decac0.png', // 易旅通默认 logo 半路径
    DATA_HOST: 'https://test.shukeyun.com/data/api/model/exec', // DATA 接口地址
    CHART_HOST: 'https://test.shukeyun.com/data/api/model/exec', // 首页报表接口地址
    HELP_URL: 'https://test.shukeyun.com/scenic/help-frontend', // 帮助中心
    FIT_URL: 'https://test.shukeyun.com/scenic/interface-designer-frontend/#/home', // 店铺装修
    FILE_HOST: 'https://minio-prod.shukeyun.com/scenic-test/', // 文件查看地址
    CHAIN_URL: 'https://test-explorer.shukechain.com', // 智旅链浏览器
    AI_HOST: 'https://test-gcluster.shukeyun.com', // ai 接口地址
    BACKEND_V2: 'https://test.shukeyun.com/scenic/backend-v2/#/', // 跳转景区
  },
  canary: {
    ENV: 'canary', // 运行环境
    BIG_DATA_HOST: 'https://canary.shukeyun.com/data/api', // 大数据接口地址
    API_HOST: 'https://canary.shukeyun.com/common/common-gateway', // 接口地址
    LOGIN_HOST: 'https://canary.shukeyun.com/cas/login', // 登录页地址
    CAS_HOST: 'https://canary.shukeyun.com/cas/web', // CAS 地址
    UPLOAD_HOST: 'https://yeahtrip.com/maintenance/deepfile/', // 图片上传地址
    IMG_HOST: 'https://yeahtrip.com/maintenance/deepfile/', // 图片预览地址
    IMG_HOSTCOMMENT: 'https://prod.shukeyun.com/maintenance/deepfile/', // 图片预览地址
    SCENIC_HOST: 'https://canary.shukeyun.com/scenic/backend-v2/', // 景区地址
    MERCHANT_HOST: 'https://canary.shukeyun.com/upaypal/hp', // 结算商户中心地址
    FINANCIAL_HOST: 'https://canary.shukeyun.com/cas/login/#/login?appId=D6wVZuOW9gmwpR9iq2MS', // 金融系统地址
    PAY_HOST: 'https://canary.shukeyun.com/upaypal/cashier/#/counter', // 结算支付地址
    MALL_HOST: 'https://canary.shukeyun.com/scenic/shop/#/', // h5 地址
    HOUSE_URL: 'https://canary.shukeyun.com/scenic/house/', // 售票窗口地址
    OPERATION_HOST: 'https://canary.shukeyun.com/data/sensor/db', // 操作日志接口
    APPID: 'hmfT05KvnbZJ9B5MYtJk', // cas 登录 appid
    DEFAULT_LOGO: 'data/2022-12-08/upload_a1c14b0ddec37ec158a04a1685decac0.png', // 易旅通默认 logo 半路径
    DATA_HOST: 'https://canary.shukeyun.com/data/api/model/exec', // DATA 接口地址
    CHART_HOST: 'https://canary.shukeyun.com/data/api/model/exec', // 首页报表接口地址
    HELP_URL: 'https://canary.shukeyun.com/scenic/help-frontend', // 帮助中心
    FIT_URL: 'https://canary.shukeyun.com/scenic/interface-designer-frontend/#/home', // 店铺装修
    FILE_HOST: 'https://minio-prod.shukeyun.com/scenic-prod/', // 文件查看地址
    CHAIN_URL: 'https://canary-explorer.shukechain.com', // 智旅链浏览器
    AI_HOST: 'https://canary-gcluster.shukeyun.com', // ai 接口地址
    BACKEND_V2: 'https://canary.shukeyun.com/scenic/backend-v2/#/', // 跳转景区
  },
  prod: {
    ENV: 'prod', // 运行环境
    BIG_DATA_HOST: 'https://prod.shukeyun.com/data/api', // 大数据接口地址
    API_HOST: 'https://yeahtrip.com/common/common-gateway', // 接口地址
    LOGIN_HOST: 'https://prod.shukeyun.com/cas/login', // 登录页地址
    CAS_HOST: 'https://cas.shukeyun.com', // CAS 地址
    UPLOAD_HOST: 'https://yeahtrip.com/maintenance/deepfile/', // 图片上传地址
    IMG_HOST: 'https://yeahtrip.com/maintenance/deepfile/', // 图片预览地址
    IMG_HOSTCOMMENT: 'https://yilvbao.cn/maintenance/deepfile/', // 图片预览地址
    SCENIC_HOST: 'https://huijingyun.net/', // 景区地址
    MERCHANT_HOST: 'https://www.upaypal.com', // 结算商户中心地址
    FINANCIAL_HOST: 'https://prod.shukeyun.com/cas/login/#/login?appId=Nk8co5lYeeWPmRO7q8Mt', // 金融系统地址
    PAY_HOST: 'https://prod.shukeyun.com/upaypal/cashier/#/counter', // 结算支付地址
    MALL_HOST: 'https://yilvbao.cn/#/', // h5 地址
    HOUSE_URL: 'https://yeahtrip.com/house/', //售票窗口地址
    OPERATION_HOST: 'https://yeahtrip.com/data/sensor/db', // 操作日志接口
    NFT_HOST: 'https://nft.zhilvlian.com/#/DigitalTickets', // NFT 浏览器
    DEFAULT_LOGO: 'data/2022-07-21/upload_3fdd2ca9bf91229192664a512fb3130c.png', // 易旅通默认 logo 半路径
    APPID: 'yc3fvV45yEWablblgUoq', // cas 登录 appid
    DATA_HOST: 'https://prod.shukeyun.com/data/api/model/exec', //DATA 接口地址
    CHART_HOST: 'https://prod.shukeyun.com/data/api/model/exec', // 首页报表接口地址
    HELP_URL: 'https://help.hly.net', // 帮助中心
    FIT_URL: 'https://prod.shukeyun.com/scenic/interface-designer-frontend/#/home', // 店铺装修
    FILE_HOST: 'https://minio-prod.shukeyun.com/scenic-prod/', // 文件查看地址
    CHAIN_URL: 'https://explorer.shukechain.com', // 智旅链浏览器
    AI_HOST: 'https://gcluster.shukeyun.com', // ai 接口地址
    BACKEND_V2: 'https://huijingyun.net/#/', // 跳转景区
  },
};

export const getEnv = () => {
  // console.log('process.env.PROXY_ENV:  ', process.env.PROXY_ENV);
  const hostname = window.location.hostname;
  if (hostname.includes('test')) {
    return envMap.test;
  } else if (hostname.includes('canary')) {
    return envMap.canary;
  } else if (hostname.includes('yeahtrip.com') || hostname.includes('prod')) {
    return envMap.prod;
  } else if (hostname.includes('local') || hostname.includes('192')) {
    if (process.env.PROXY_ENV === 'test') {
      return { ...envMap.test, ...envMap.local };
    } else if (process.env.PROXY_ENV === 'canary') {
      return { ...envMap.canary, ...envMap.local };
    } else if (process.env.PROXY_ENV === 'prod') {
      return { ...envMap.prod, ...envMap.local };
    }
    return { ...envMap.dev, ...envMap.local };
  } else {
    return envMap.dev;
  }
};
