import ExportButton from '@/common/components/ExportButton';
import useExport from '@/common/components/ExportButton/useExport';
import ProModal from '@/common/components/ProModal';
import useProModal from '@/common/components/ProModal/useProModal';
import TimeStore from '@/common/components/TimeStore';
import { columnsState, tableConfig } from '@/common/utils/config';
import { inventoryRecord, productTypeEnum, stockType, ticketTypeEnum } from '@/common/utils/enum';
import { modelWidth } from '@/common/utils/gConfig';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getUniqueId } from '@/common/utils/tool';
import type { ModalState } from '@/hooks/useModal';
import {
  apiStockChangeRecord,
  getDistributorStockTotalInfo,
  getTimeShareAndStockDetail,
} from '@/services/api/distribution';
import type { API } from '@/services/api/typings';
import type { ProFormColumnsType } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-table';
import { Descriptions, Modal, Tag } from 'antd';
import type { FC } from 'react';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from '@umijs/max';
import styles from '../index.less';

interface DetailModalProps {
  dataItem?: API.TotalStockItem;
  modalState: ModalState;
  // 是否分时预约
  isTimeShare: boolean;
  headerTitle?: string;
  dataSource?: API.EnterTimeDetailListItem[];
}

const DetailModal: FC<DetailModalProps> = ({
  modalState: { visible, setVisible },
  headerTitle,
  isTimeShare,
  dataSource = [],
  dataItem,
}) => {
  // 分时库存
  const timeModalState = useProModal();
  const [timeModalData, setTimeModalData] = useState<any>({});
  const [lawsList, setLawsList] = useState<any>([]);
  const lawsColumns = [
    {
      title: ({ beginTime, endTime }: any) => `分时时段：${[beginTime, endTime].join('-')}`,
      render: () => {},
    },
    {
      title: () => '可用库存/总库存量',
      render: ({ availableNumber = 0, stockAmount = 0 }) => `${availableNumber}/${stockAmount}`,
    },
  ];
  const timeModalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '入园有效时间',
          valueType: 'dateRange',
          dataIndex: 'dateRange',
          initialValue: [timeModalData.enterStartTime, timeModalData.enterEndTime],
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '',
          dataIndex: 'timeLaws',
          colProps: { span: 24 },
          renderFormItem: () => <TimeStore lawsColumns={lawsColumns} lawsList={lawsList} />,
        },
        {
          title: '可用库存',
          dataIndex: 'availableNumberSum',
          fieldProps: {
            disabled: true,
          },
        },
        {
          title: '总库存量',
          dataIndex: 'stockAmountSum',
          fieldProps: {
            disabled: true,
          },
        },
      ],
    },
  ];

  const { initialState } = useModel('@@initialState');
  const { coId } = initialState?.currentCompany || {};
  // 详情
  const { loading, data, run } = useRequest(getTimeShareAndStockDetail, {
    manual: true,
    formatResult(res) {
      return res.data.list;
    },
    initialData: dataSource,
    onSuccess(data, params) {
      addOperationLogRequest({
        action: 'info',
        content: `查看产品【${dataItem?.productName}】票务库存详情`,
      });
    },
  });

  // 分时表格配置
  const timeShareColumns: ProColumns<API.EnterTimeDetailListItem>[] = [
    {
      title: '批次号',
      dataIndex: 'batchId',
    },
    {
      title: '分时预约',
      dataIndex: 'timeBeginTime',
      render: (_, { timeShareBeginTime, timeShareEndTime }) => (
        <span>{timeShareBeginTime + ' 至 ' + timeShareEndTime}</span>
      ),
    },
    {
      title: '当前库存',
      dataIndex: 'number',
      align: 'right',
      valueType: 'digit',
    },
  ];
  // 非分时表格配置
  const columns: ProColumns<API.EnterTimeDetailListItem>[] = [
    {
      title: '库存批次号',
      dataIndex: 'masterBatch',
      render: (dom: any, record, index, __, schema: any) => (
        <span>
          {dom} {record.isExchange === 1 && <Tag color="blue">交易所</Tag>}
        </span>
      ),
    },
    {
      title: '库存类型',
      dataIndex: 'stockType',

      render: (_, { stockType }) => {
        const enumm = {
          1: '区块链',
          0: '非区块链',
        };
        return enumm[stockType];
      },
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      render: (_, { goodsName, goodsId }) => {
        return goodsName;
      },
    },
    {
      title: '购买有效时间',
      dataIndex: 'purchaseBeginTime',
      render: (_, { purchaseBeginTime, purchaseEndTime }) => (
        <span>
          {purchaseBeginTime && purchaseEndTime
            ? purchaseBeginTime + ' 至 ' + purchaseEndTime
            : '--'}
        </span>
      ),
    },
    {
      title: '入园有效时间',
      dataIndex: 'enterStartTime',
      render: (_, { enterStartTime, enterEndTime }) => (
        <span>
          {enterStartTime && enterEndTime ? enterStartTime + ' 至 ' + enterEndTime : '--'}
        </span>
      ),
    },
    {
      title: '总库存量',
      dataIndex: 'totalNumber',
    },
    {
      title: '可用库存',
      dataIndex: 'availableNumber',
    },
    {
      title: '已售库存',
      dataIndex: 'spendNumber',
    },
    {
      title: '冻结库存（贷款）',
      dataIndex: 'newFreezeNum',
    },
    {
      title: '冻结库存（挂单）',
      dataIndex: 'positionFreezeNum',
    },
    {
      title: '过期库存',
      dataIndex: 'expiredStock',
    },
    {
      title: '销毁库存',
      dataIndex: 'destroyInventoryNum',
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_: any, entity: any) =>
        entity.timeShareId ? (
          <a
            key={getUniqueId()}
            onClick={() => {
              getDistributorStockTotalInfo({
                masterBatch: entity.masterBatch,
                ticketGoodsId: dataItem.goodsId,
                distributorId: coId,
              }).then(({ data }) => {
                setLawsList(data.timeShareVoList);
                setTimeModalData({
                  enterStartTime: entity?.enterStartTime,
                  enterEndTime: entity?.enterEndTime,
                  timeLaws: data.timeLaws,
                  availableNumberSum: data.availableNumberSum,
                  stockAmountSum: data.stockAmountSum,
                });
                timeModalState.setType('edit');
              });
            }}
          >
            分时详情
          </a>
        ) : '',
    },
  ];

  // 库存变化记录
  const recordColumns: ProColumns<API.EnterTimeDetailListItem>[] = [
    {
      title: '变化时间',
      dataIndex: 'createTime',
      search: false,
    },
    {
      title: '库存批次号',
      dataIndex: 'batchId',
      render: (dom: any, record, index, __, schema: any) => (
        <span>
          {dom} {record.isExchange === 1 && <Tag color="blue">交易所</Tag>}
        </span>
      ),
    },
    {
      title: '库存类型',
      dataIndex: 'stockType',
      valueEnum: stockType,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '库存变化类型',
      dataIndex: 'stockChangeType',
      valueEnum: inventoryRecord,
    },
    {
      title: '变化时间',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (text) => ({
          startTime: text[0],
          endTime: text[1],
        }),
      },
    },
    {
      title: '订单/退单号',
      dataIndex: 'orderId',
    },
    {
      title: '变化数量',
      dataIndex: 'changeNum',
      search: false,
    },
    {
      title: '总库存量',
      dataIndex: 'totalStockNum',
      search: false,
    },
    {
      title: '可用库存',
      dataIndex: 'availableStockNum',
      search: false,
    },
    {
      title: '已售库存',
      dataIndex: 'saleStockNum',
      search: false,
    },
    {
      title: '冻结库存（贷款）',
      dataIndex: 'freezeStockNum',
      search: false,
    },
    {
      title: '冻结库存（挂单）',
      dataIndex: 'positionFreezeNum',
      search: false,
    },
    {
      title: '过期库存',
      dataIndex: 'expiredStockNum',
      search: false,
    },
    {
      title: '销毁库存',
      dataIndex: 'destroyInventoryNum',
      search: false,
    },
  ];
  const exportState = useExport('/distribution/distribution/stockChangeRecord', recordColumns, {
    goodsId: dataItem?.goodsId,
    belonger: coId,
  });

  useEffect(() => {
    if (visible && dataItem && !isTimeShare) {
      const { productId, categoryType } = dataItem;
      run({
        productId,
        distributorId: coId,
        categoryType,
      });
    }
  }, [visible, isTimeShare]);
  return (
    <Modal
      width={modelWidth.xl}
      title={isTimeShare ? '分时预约信息' : '库存详情'}
      open={visible}
      destroyOnClose
      footer={null}
      onCancel={() => {
        setVisible(false);
      }}
    >
      <Descriptions title="基本信息" style={{ marginTop: 20 }}>
        <Descriptions.Item label="景区名称">{dataItem?.scenicName}</Descriptions.Item>
        <Descriptions.Item label="产品名称">{dataItem?.productName}</Descriptions.Item>
        <Descriptions.Item label="供应商名称">{dataItem?.supplierNames}</Descriptions.Item>
        <Descriptions.Item label="产品类型">{productTypeEnum[dataItem?.proType]}</Descriptions.Item>
        <Descriptions.Item label="商品名称">{dataItem?.goodsName}</Descriptions.Item>
        <Descriptions.Item label="票种">
          {ticketTypeEnum[dataItem?.ticketGoodsType]}
        </Descriptions.Item>
      </Descriptions>
      <Descriptions title="批次信息" style={{ marginTop: 20 }} className="no-bgColor">
        <Descriptions.Item>
          <ProTable<API.EnterTimeDetailListItem>
            {...tableConfig}
            cardBordered
            style={{ width: '100%' }}
            rowKey="batchId"
            loading={loading}
            headerTitle={headerTitle}
            columns={isTimeShare ? timeShareColumns : columns}
            dataSource={isTimeShare ? data : dataItem?.stockList}
            rowClassName={(record) => (record.colType === 'total' ? styles.total_row : '')}
            search={false}
            options={false}
            pagination={false}
          />
        </Descriptions.Item>
      </Descriptions>
      <Descriptions title="库存变化记录" style={{ marginTop: 20 }} className="no-bgColor">
        <Descriptions.Item>
          <ProTable<API.EnterTimeDetailListItem>
            {...tableConfig}
            bordered
            style={{ width: '100%' }}
            columns={recordColumns}
            params={{ goodsId: dataItem?.goodsId, belonger: coId }}
            request={apiStockChangeRecord}
            formRef={exportState.formRef}
            columnsState={columnsState(exportState)}
            toolBarRender={() => [<ExportButton {...{ exportState }} />]}
          />
        </Descriptions.Item>
      </Descriptions>
      {/* 分时库存 */}
      <ProModal
        {...timeModalState}
        fullTitle="分时预约详情"
        columns={timeModalColumns}
        layout="horizontal"
        dataSource={timeModalData}
        onFinish={async () => true}
      />
    </Modal>
  );
};

export default DetailModal;
