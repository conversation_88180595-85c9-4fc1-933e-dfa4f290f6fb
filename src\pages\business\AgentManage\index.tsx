import Delete from '@/common/components/Delete';
import ImageUpload from '@/common/components/FileUpload';
import ProModal from '@/common/components/ProModal';
import useModal from '@/common/components/ProModal/useProModal';
import useProModal from '@/common/components/ProModal/useProModal';
import AgentPreview from './components/AgentPreview';
import { tableConfig } from '@/common/utils/config';
import { AgentFigureTypeEnum } from '@/common/utils/enum';
import { addOperationLogRequest } from '@/common/utils/operationLog';
import { getHashParams } from '@/common/utils/tool';
import {
  agentCreate,
  deleteAgent,
  editAgent,
  getAgentDetail,
  getAgentList,
} from '@/services/api/agent';
import { PlusOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { ProFormColumnsType } from '@ant-design/pro-components';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Image, message, Tooltip, Space, Radio, Upload, Modal } from 'antd';
import Input from 'antd/es/input/Input';
import TextArea from 'antd/es/input/TextArea';
import { useEffect, useRef, useState, useMemo } from 'react';
import ImgCrop from 'antd-img-crop';
import { documentHost } from '@/services/api';
import type { UploadFile, RcFile } from 'antd/lib/upload/interface';

// 带裁剪功能的图片上传组件
const CroppedImageUpload = ({ value, onChange, defaultValue }: any) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  // 初始化文件列表
  useEffect(() => {
    if (defaultValue && defaultValue.length > 0) {
      const initialFileList = defaultValue.map((item: any, index: number) => ({
        uid: `${index}`,
        name: item.fileName || `image-${index}`,
        status: 'done' as const,
        url: item.fileUrl,
        thumbUrl: item.fileUrl,
      }));
      setFileList(initialFileList);
    }
  }, [defaultValue]);

  // 上传前验证
  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只允许上传 JPG/PNG 格式的图片!');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片必须小于 2MB!');
      return false;
    }
    return true;
  };

  // 处理上传变化
  const handleChange = ({ fileList: newFileList }: any) => {
    setFileList(newFileList);

    // 处理上传成功的文件
    const uploadedFiles = newFileList
      .filter((file: UploadFile) => file.status === 'done' && file.response)
      .map((file: UploadFile) => ({
        id: file.response?.data?.fileId || file.uid,
        fileUrl: file.response?.data?.accessUrl || file.url,
        fileName: file.response?.data?.name || file.name,
      }));

    if (uploadedFiles.length > 0 && onChange) {
      onChange(uploadedFiles);
    }
  };

  return (
    <ImgCrop
      showGrid
      quality={0.8}
      aspect={9 / 20}
      modalTitle="裁剪形象照片"
      modalOk="确定"
      modalCancel="取消"
    >
      <Upload
        listType="picture-card"
        fileList={fileList}
        maxCount={1}
        beforeUpload={beforeUpload}
        onChange={handleChange}
        action={documentHost + '/file/custom/uploadFile'}
        headers={{
          'system-type': 'e-commerce',
        }}
      >
        {fileList.length < 1 && (
          <div>
            <PlusOutlined />
            <div style={{ marginTop: 8 }}>上传</div>
          </div>
        )}
      </Upload>
    </ImgCrop>
  );
};

// 拟人形象选择组件
const AvatarSelector = ({ value, onChange, defaultValue }: any) => {
  const [visible, setVisible] = useState(false);
  const [selectedAvatar, setSelectedAvatar] = useState<any>(null);

  // 拟人形象数据
  const avatarList = useMemo(
    () => [
      {
        id: 1,
        name: '拟人形象',
        imageUrl:
          'https://minio-test.shukeyun.com/exchange-test/1757401112962dbd61f72d34c9915325fe41c03e1046.jpg',
      },
    ],
    [],
  );

  // 初始化选中的形象
  useEffect(() => {
    const currentValue = value || defaultValue;
    if (currentValue && currentValue.length > 0) {
      const currentAvatar = avatarList.find(
        (avatar) => avatar.imageUrl === currentValue[0].fileUrl,
      );
      setSelectedAvatar(currentAvatar);
    }
  }, [value, defaultValue, avatarList]);

  const handleSelectAvatar = (avatar: any) => {
    setSelectedAvatar(avatar);
    if (onChange) {
      onChange([
        {
          id: avatar.id,
          fileUrl: avatar.imageUrl,
          fileName: avatar.name,
        },
      ]);
    }
    setVisible(false);
  };

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        {selectedAvatar && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Image
              src={selectedAvatar.imageUrl}
              alt={selectedAvatar.name}
              style={{ width: 60, height: 120, objectFit: 'cover', borderRadius: '4px' }}
              preview={false}
            />
            <div>
              <div style={{ fontWeight: 'bold' }}>{selectedAvatar.name}</div>
            </div>
          </div>
        )}
        <Button type="primary" onClick={() => setVisible(true)}>
          {selectedAvatar ? '更换形象' : '选择形象'}
        </Button>
      </div>

      <Modal
        title="选择拟人形象"
        open={visible}
        onCancel={() => setVisible(false)}
        footer={null}
        width={300}
        style={{ top: 20 }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            padding: '16px 0',
          }}
        >
          {avatarList.map((avatar) => (
            <div
              key={avatar.id}
              style={{
                cursor: 'pointer',
                border:
                  selectedAvatar?.id === avatar.id ? '3px solid #1890ff' : '2px solid #d9d9d9',
                borderRadius: '8px',
                overflow: 'hidden',
                transition: 'all 0.3s ease',
              }}
              onClick={() => handleSelectAvatar(avatar)}
            >
              <Image
                src={avatar.imageUrl}
                alt={avatar.name}
                style={{ width: 200, height: 400, objectFit: 'cover', display: 'block' }}
                preview={false}
              />
            </div>
          ))}
        </div>
      </Modal>
    </>
  );
};

export default () => {
  const queryParams = getHashParams();
  // const { updateGuideInfo } = useGuide();
  const modalState = useModal();
  const previewModalState = useProModal();
  const actionRef = useRef<ActionType>();
  const [id, setId] = useState<string | null>();
  const coId = localStorage.getItem('currentCompanyId');

  // 新增按钮
  const addButton = (
    <Button
      key="k1"
      type="primary"
      onClick={() => {
        setId(null);
        modalState.setType('add');
      }}
    >
      <PlusOutlined /> 新增
    </Button>
  );
  const tableColumns: ProColumnType[] = [
    {
      title: '虚拟导游名称',
      width: 250,
      dataIndex: 'name',
      render: (dom, record: any) => {
        return (
          <div
            style={{
              width: 'auto',
              maxWidth: '500px',
              whiteSpace: 'normal',
              wordWrap: 'break-word',
            }}
          >
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
              <Image
                key={record?.id}
                width={80}
                height={80}
                style={{ objectFit: 'cover', borderRadius: '4px' }}
                src={`${record?.iconUrl}`}
                preview={{ src: `${record?.iconUrl}` }}
              />
              <span style={{ fontWeight: 700 }}>{record?.name}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: (
        <span>
          形象类型
          <Tooltip
            placement="top"
            title={
              <div style={{ whiteSpace: 'pre-line' }}>
                {`拟人形象：支持选择仿真人形象，实现动画形象对话\n图片形象：支持上传自定义图片，静态形象展示`}
              </div>
            }
          >
            <InfoCircleOutlined style={{ marginLeft: 6, color: '#999' }} />
          </Tooltip>
        </span>
      ),
      width: 100,
      dataIndex: 'figureType',
      search: false,
      render: (value: number) => AgentFigureTypeEnum?.[value] || '-',
    },
    {
      title: '简介',
      width: 700,
      dataIndex: 'introduction',
      search: false,
      // 修复简介文本换行问题
      render: (text: string) => (
        <div
          style={{
            minWidth: '300px',
            maxWidth: '880px', // 限制最大宽度
            whiteSpace: 'normal', // 允许正常换行
            wordBreak: 'break-word', // 长单词强制拆分换行
            lineHeight: 1.6, // 增加行高，提升可读性
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: '操作',
      width: 200,
      valueType: 'option',
      render: (_: any, entity: any) => (
        <Space size="middle">
          <a
            onClick={() => {
              setId(entity.id);
              previewModalState.setType('info');
              addOperationLogRequest({
                action: 'info',
                content: `预览【${entity.name}】虚拟导游`,
              });
            }}
            key="k1"
          >
            预览
          </a>
          <a
            onClick={() => {
              setId(entity.id);
              modalState.setType('edit');
            }}
            key="k2"
          >
            编辑
          </a>
          <Delete
            key="k3"
            access={true}
            status={false}
            params={{ id: entity.id }}
            content={`删除虚拟导游"${entity.name}"后，该虚拟导游的所有应用将同时删除`}
            request={async (params) => {
              const data = await deleteAgent(params.id);
              addOperationLogRequest({
                action: 'del',
                content: `删除【${entity.name}】虚拟导游`,
              });
              return data;
            }}
            actionRef={actionRef}
          />
        </Space>
      ),
    },
  ];
  const modalColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          width: 600,
          title: '虚拟导游名称',
          dataIndex: 'name',
          renderFormItem: () => (
            <Input showCount={true} placeholder="给我取一个名称" maxLength={20} />
          ),
          formItemProps: {
            rules: [
              { required: true, message: '请输入虚拟导游名称' },
              { max: 20, message: '虚拟导游名称最多输入20字' }, // 新增长度校验
            ],
          },
        },
      ],
    },
    {
      title: '',
      columns: [
        {
          width: 600,
          title: (
            <span>
              形象类型
              <Tooltip
                placement="top"
                title={
                  <div style={{ whiteSpace: 'pre-line' }}>
                    {`拟人形象：支持选择仿真人形象，实现动画形象对话\n图片形象：支持上传自定义图片，静态形象展示`}
                  </div>
                }
              >
                <InfoCircleOutlined style={{ marginLeft: 6, color: '#999' }} />
              </Tooltip>
            </span>
          ),
          dataIndex: 'figureType',
          initialValue: 1,
          renderFormItem: () => (
            <Radio.Group
              options={[
                { label: '拟人形象', value: 1 },
                { label: '图片形象', value: 0 },
              ]}
            />
          ),
        },
      ],
    },
    {
      title: '',
      columns: [
        {
          width: 600,
          title: '形象照片',
          dataIndex: 'iconUrl',
          dependencies: ['figureType'], // 依赖形象类型字段
          formItemProps: { rules: [{ required: true, message: '请上传形象照片' }] },
          renderText: (text: any) => <ImageUpload defaultValue={text} readonly />,
          renderFormItem: (_, __, formRef) => {
            const figureType = formRef.getFieldValue('figureType');
            const defaultValue = formRef.getFieldValue('iconUrl');

            // 根据形象类型选择不同的组件
            if (figureType === 1) {
              // 拟人形象：显示形象选择器
              return <AvatarSelector defaultValue={defaultValue} key="avatar-selector" />;
            } else {
              // 图片形象：显示图片上传组件
              return <CroppedImageUpload defaultValue={defaultValue} key="image-upload" />;
            }
          },
        },
      ],
    },
    {
      title: '',
      columns: [
        {
          width: 600,
          title: '虚拟导游简介',
          dataIndex: 'introduction',
          formItemProps: {
            rules: [
              { required: true, message: '请描述虚拟导游简介' },
              { max: 200, message: '虚拟导游简介最多输入200字' }, // 新增长度校验
            ],
            getValueFromEvent: (e: any) => e.target.value,
          },
          renderFormItem: () => (
            <TextArea
              placeholder="描述形象特征、风格，如卡通形象、人物、拟人化动物、性格、外形元素等"
              autoSize={{ minRows: 3, maxRows: 6 }}
              maxLength={200} // 限制最大输入长度
              showCount={true}
            />
          ),
        },
      ],
    },
    {
      title: '',
      columns: [
        {
          width: 600,
          title: '开场白',
          dataIndex: 'openerPrompt',
          formItemProps: {
            rules: [
              { required: true, message: '请输入开场白' },
              { max: 200, message: '开场白最多输入200字' }, // 新增长度校验
            ],
            getValueFromEvent: (e: any) => e.target.value,
          },
          renderFormItem: () => (
            <TextArea
              placeholder="请输入开场白内容"
              autoSize={{ minRows: 3, maxRows: 6 }}
              maxLength={200}
              showCount={true}
            />
          ),
        },
      ],
    },
  ];

  // 预览弹窗的列配置
  const previewColumns: ProFormColumnsType[] = [
    {
      title: '',
      columns: [
        {
          title: '',
          dataIndex: 'previewContent',
          colProps: { span: 24 },
          render: (_, record) => <AgentPreview data={record} />,
        },
      ],
    },
  ];

  useEffect(() => {
    if (queryParams?.openCreate === '1') {
      setId(null); // 新增模式下 id 为 null
      modalState.setType('add'); // 触发新增弹框
    }
  }, []); // 空依赖，仅挂载时执行

  return (
    <>
      <ProTable
        {...tableConfig}
        style={previewModalState.tableStyle}
        actionRef={actionRef}
        columns={tableColumns}
        toolBarRender={() => [addButton]}
        params={{ coId }}
        pagination={{
          pageSize: 10, // 强制每页显示10条（核心配置）
          // showQuickJumper: true, // 显示快速跳转页码（可选，提升效率）
        }}
        request={(params) => {
          return getAgentList(params);
        }}
      />
      <ProModal
        {...modalState}
        title="虚拟导游"
        actionRef={actionRef}
        columns={modalColumns}
        params={{ id, coId }}
        infoRequest={async (params) => {
          try {
            const res = await getAgentDetail(params.id);
            const detailData = res.data || res;
            const formattedData = {
              coId,
              name: detailData.name || detailData.agentName || '',
              iconUrl: detailData.iconUrl ? [{ fileUrl: detailData.iconUrl }] : [],
              introduction: detailData.introduction || detailData.desc || '',
              openerPrompt: detailData.openerPrompt || detailData.welcomeMsg || '',
              figureType: detailData.figureType,
            };
            // 返回符合ProModal期望的格式
            return { data: formattedData };
          } catch (error) {
            message.error('获取虚拟导游详情失败');
            console.error(error);
            return { data: {} };
          }
        }}
        addRequest={async (params) => {
          params.backgroundImageUrl = params.iconUrl[0].fileUrl;
          params.iconUrl = params.iconUrl[0].fileUrl;
          params.instruction = params.introduction;
          params.figureType = params.figureType;
          const data = await agentCreate(params);
          // 更新引导
          // updateGuideInfo({ tabIndex: 1, status: GuideStepStatus.step1_5 });
          addOperationLogRequest({
            action: 'add',
            content: `新增【${params.name}】虚拟导游`,
          });
          return data;
        }}
        editRequest={async (params) => {
          // 确保传递完整的参数，包括id
          const requestData: any = {
            coId,
            name: params.name,
            backgroundImageUrl: params.iconUrl[0].fileUrl,
            iconUrl: params.iconUrl[0].fileUrl,
            instruction: params.introduction,
            introduction: params.introduction,
            openerPrompt: params.openerPrompt,
            figureType: params.figureType,
            // 根据API需要添加其他字段
          };

          const data = await editAgent(params.id, requestData);
          addOperationLogRequest({
            action: 'edit',
            content: `编辑【${params.name}】虚拟导游`,
          });
          return data;
        }}
      />

      {/* 预览弹窗 - 内嵌模式 */}
      <ProModal
        page
        {...previewModalState}
        title="虚拟导游"
        columns={previewColumns}
        params={{ id, coId }}
        infoRequest={async (params) => {
          try {
            const res = await getAgentDetail(params.id);
            const detailData = res.data || res;
            const formattedData = {
              coId,
              name: detailData.name || detailData.agentName || '',
              iconUrl: detailData.iconUrl ? [{ fileUrl: detailData.iconUrl }] : [],
              introduction: detailData.introduction || detailData.desc || '',
              openerPrompt: detailData.openerPrompt || detailData.welcomeMsg || '',
              figureType: detailData.figureType,
            };
            return { data: formattedData };
          } catch (error) {
            message.error('获取虚拟导游详情失败');
            console.error(error);
            return { data: {} };
          }
        }}
      />
    </>
  );
};
