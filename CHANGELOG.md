# Changelog

All notable changes to this project will be documented in this file.

## [unreleased]

### 🚀 Features

- 添加 eslint 增量校验功能
- 更新代理配置以支持 WebSocket 并优化 AI 对话接口的流式处理
- 添加 DataMask 组件
- 添加数据脱敏功能，支持姓名、身份证和手机号的脱敏处理
- 优化数据脱敏功能，支持自动识别脱敏类型（姓名、身份证、手机号）
- 增加数据脱敏状态控制，优化手机号脱敏显示逻辑
- 在 DataMask 组件中添加操作日志记录功能，并在 OrderForm 中传递日志内容
- 在 DataMask 组件中添加节流功能以优化操作日志记录，支持自定义节流时间
- 更新 DetailsPop 组件的标题类型为支持 React 节点，并在 OrderForm 中优化数据脱敏逻辑，添加对联系人信息的脱敏处理
- 在 OrderForm 中优化查看所有功能，添加对联系人信息的掩码处理逻辑
- 更新 useMask Hook 的返回值结构，优化 OrderForm 和 OrderReturn 组件中数据脱敏逻辑，添加对退单和子订单详情的掩码处理
- 在多个组件中引入 DataMask 和 useMask，优化用户隐私信息的脱敏处理逻辑，增强订单和退订详情的展示效果
- 在 CommissionBill、Byshop 和 Bysupplier 组件中集成 DataMask 和 useMask，增强用户隐私信息的脱敏处理，优化弹窗标题和详情展示
- 优化 dataMask 函数，默认使用姓名类型进行脱敏处理，简化类型识别逻辑，增强手机号和身份证的脱敏显示效果
- 在 Welcome 页面中优化按钮点击逻辑，支持单一商店或景区直接跳转链接并记录操作日志
- 增加预警路由
- 开发库存模块静态
- 发送消息通知选择员工静态开发
- 对接选择员工接口
- 对接选择用户
- 对接并优化逻辑添加用户
- 选择员工对接
- 接收人表格和选择员工表格对应
- 选择员工对接完成以及用户管理跳转完成
- 绩效统计路由添加
- 开发经销商代理商静态
- Antv 图表研究以及对接开发
- 数据父子组件逻辑优化
- 对接经销商
- 对接经销商数据，并处理处理
- 绩效统计流向图数据对接
- 优化图表展示以及各种逻辑优化
- 图表对接以及优化放大缩小和拖拽以及防抖
- 经销商和代理商完成开发
- 消息通知查询接口对接
- 消息通知查询对接以及标记单项对接
- 库存字段对接
- 对接库存预警
- 库存预警字段数据拿到对接
- 库存预警接口对接以及产品下拉和商品下拉对接
- 对接库存回显
- 对接库存预警回显
- 对接编辑接口以及回显
- 消息暂无数据优化
- 库存预警接口联调完成
- 在支付类型枚举中添加'其他支付'选项
- 绩效统计推生产，消息模块暂时隐藏
- 提交分支名,提交信息，提交信息管理议题 id 功能
- 绩效统计模块提示优化，样式部分修改
- 更多功能静态框开发
- 智能客服默认框静态
- 编辑价格策略增加字段
- 给后端传字段方便消息推送的开发
- 智能客服优化
- 一票多人门票实际售票数开发
- 采购订单前往采购字段带过来
- 前往采购采购订单开发完成
- 跳转逻辑开发
- 注释掉 sonar 任务
- 库存兼容挂单开发联调
- 增加“回到默认视图”的功能
- 有新消息后，小铃铛上需要显示具体的消息数量
- 消息通知已读未读
- 消息通知优化
- 采购订单商品字段带过来
- 前往采购字段回填
- 数据检索和票务管理静态开发
- 智能客服静态基本完成
- 用户增加启用禁用，合作邀请增加字段
- 邀请增加字段
- 页面点击两次提价报错，增加必填校验
- 修复邀请的跳转字段
- 在订单详情中增加数据掩码功能，保护用户隐私信息
- 智能客服数据检索静态
- 智能客服静态开发
- 数据检索静态
- 票务管理静态
- 消息列表静态优化
- 智能客服按钮展示样式优化
- 在 ProductConfig 组件中添加搜索功能并更新.gitignore 文件以忽略 docs 目录
- 换一换功能开发
- 库存预警价格策略新增
- 库存预警 ui 优化
- 优化库存预警 ui
- 库存预警折叠面板优化逻辑
- 库存预警 UI 优化任务完成
- 打开客服弹框请求保存个人信息接口
- 对接在消息中增加字段
- 将直销的弹窗抽离出来
- 添加动态定价功能，重构 SelfStrategyModal 组件以支持固定和动态定价表单切换
- 在 SelfStrategyModal 中添加天气、日承载量和淡旺季策略的动态表单项，优化策略行组件以支持条件和参数设置
- 在 SelfStrategyModal 中优化价格策略表单项的样式，添加成本限制和组合销售选项，增强用户体验
- 在 SelfStrategyModal 中添加已添加策略列表的显示功能，优化表单布局和样式，增强用户交互体验
- 在 SelfStrategyModal 中添加价格历史图表、价格调整列表和入口统计图表，优化用户界面展示
- 在 StrategyListModal 中添加定价建议和更新日志功能的提示信息
- 添加定价建议弹窗
- 添加分支名和提交信息检查功能，增强提交信息管理
- 修改分支名
- 修改 GIT_URL 命名
- 屏蔽环境变量打印
- 将分支名包管理工具从 pnpm 修改为 yarn
- 清除原来的安装包
- 注释删除 node_modules 的逻辑
- 将设置价格策略从弹窗改成页面
- 在价格策略列表中添加标签页切换功能，支持代理和直销策略的展示
- 在价格策略列表中引入自定义策略内容组件，替代直销价格策略配置区域
- 更新自定义策略内容组件，增加价格概览和日历功能，样式抽离成独立文件
- 更新自定义策略内容组件样式，优化标签和警告图标展示
- 更新自定义策略内容组件样式，新增日历布局和侧边栏功能
- 更新价格策略相关组件，移除自定义策略模态框，优化样式和功能
- 更新自定义策略内容组件，优化日历数据生成逻辑，新增今日高亮样式
- 在动态定价策略模态框中新增商品信息获取功能，优化价格策略表单布局和样式
- 在动态定价策略模态框中新增成本限制和更新方式字段，优化子策略开关逻辑和表单验证
- 更新自定义策略内容组件，新增价格悬停效果样式，优化日历价格展示逻辑
- 更新自定义策略内容组件，新增价格设置弹窗功能，优化价格展示逻辑和样式
- 更新自定义策略内容组件，新增组合销售弹窗功能，优化组合销售展示逻辑和样式
- 在自定义策略内容组件中新增日期选择器功能，优化日历样式和展示逻辑
- 更新策略列表组件，新增正常价格内容展示逻辑，优化自定义策略内容切换
- 更新正常价格内容组件，新增商品信息缓存获取逻辑，优化价格展示逻辑
- 更新正常价格内容组件，新增加载状态和商品信息回显逻辑，优化价格计算和展示逻辑
- 更新策略列表组件，新增自定义价格策略提交逻辑，优化价格展示和版本切换功能
- 更新自定义策略内容组件，优化价格设置逻辑，新增价格和折扣计算功能，调整样式和布局
- 更新价格设置弹窗逻辑，新增按星期选择日期功能，优化价格保存和验证逻辑
- 在自定义策略内容组件中新增销售日期动态列表功能，优化价格展示逻辑和状态管理
- 更新策略列表和自定义策略内容组件，新增商品信息缓存获取逻辑，优化开关状态管理和加载状态展示
- 在策略列表组件中新增漫游式引导功能，优化引导状态管理和步骤配置
- 更新自定义策略内容组件，新增日历编辑模式和批量设置功能，优化价格设置逻辑和样式
- 更新价格设置和自定义策略内容组件，新增商品信息表格和组合销售功能，优化价格计算逻辑和样式
- 在策略列表组件中新增动态定价和单买价格的保存逻辑，优化开关状态管理
- 在策略列表中新增价格策略设置开关接口，优化动态定价和单买价格的状态切换逻辑
- 更新策略列表组件，禁用价格策略设置开关，优化状态管理逻辑
- 更新价格设置接口，调整参数传递方式为 params
- 优化价格范围显示逻辑，简化条件判断，确保始终显示价格区间
- 智能客服联调功能查找
- 样式优化智能客服
- 库存预警对接智能客服
- 库存预警对接
- 整体逻辑优化
- 引入 GoodsTable 组件，重构 NormalPriceContent 以简化表格渲染逻辑
- 重构价格设置和自定义策略内容，使用 GoodsTable 组件替换原有表格，优化数据加载逻辑
- 扩展 API 类型，新增销售日期动态列表和功能启用标志，同时调整策略列表中的标签顺序
- 将价格设置模态框重构为表单形式，优化状态管理和数据回显逻辑，增强用户体验 #355
- 价格策略默认打开设置框
- 价格策略完成对接
- 数据检索开发
- 库存预警完成对接
- 宽度展示优化
- 设置价格前后 hover 热区大小不同
- 更新商品详情和价格展示，添加“起”字样以明确价格起始信息
- 更新价格历史图表组件，整合样式并优化日期范围选择逻辑
- 添加天气趋势图组件
- 更新价格设置模态框样式，优化表格布局和单元格显示
- 添加基础环境变量
- 添加大数据接口支持，更新天气趋势图组件以获取天气数据
- 评论管理 tab 加上
- 评价管理静态开发
- 增加回复弹框
- 标签管理静态页完成开发
- 触发重构
- 原生拖拽功能开发
- 完成标签拖拽以及删除功能开发还无对接
- 评价标签接口对接完成
- 更新 Husky 脚本以从远程获取配置并简化本地逻辑
- 对接入园情况图表接口
- 优化价格和折扣计算逻辑
- 对接评论管理后台
- 评论对接
- 提交评论完成开发
- 联调修改推荐功能
- 联调后台完成
- 智能客服跳转问题修复
- 联调问题修复
- 将 yarn 升级为 pnpm
- 补充 pnpm 依赖包
- 更新线上支付收银台地址
- 视频预览开发
- 销售授权调整
- 销售授权重构
- 开发销售授权
- 销售授权优化
- 梳理逻辑开发授权
- 销售授权模块联调
- 完成编辑对接
- 联调销售授权
- 重构环境配置和构建流程，优化动态 publicPath 设置
- 启用 husky pre-commit 脚本执行
- 修改生产环境域名判断条件
- 销售授权处理
- 更新 ShopNav 组件样式，添加图标占位符和图标块布局
- 优化 ShopNav 组件样式，调整图标布局和容器样式，增强可读性和交互性
- 重构 ShopNav 组件，替换 Modal 为 IconSelectModal，简化图标选择逻辑
- 更新 ShopNav 和相关组件，增强图标选择功能，支持自定义图标的增删改查 #677 #712
- 增强 ShopNav 组件，添加图标选择提示和样式，优化表单校验逻辑
- 更新 IconSelectModal 组件，添加空状态提示，优化自定义图标展示逻辑
- 更新 ShopNav 组件，增强导航样式选择功能，优化表单逻辑和状态管理
- 更新 ShopNav 组件，添加舵式导航样式，优化导航项布局和状态管理
- 更新 ShopNav 组件，添加 linkType 属性以支持内部和外部链接，优化图标选择和表单逻辑
- 为舵式导航中间项添加默认图片功能，优化图片上传逻辑和样式，支持圆形图标显示 #684
- 修复导航组件图片显示问题，优化样式逻辑，确保预览区域与配置区域一致性 #686 #712
- 添加行业虚拟导游模版
- 优化 CreateIconModal 组件，增强表单验证逻辑，确保图标名称和文件上传有效性
- 修改删除图标确认弹窗标题和内容，增强用户提示信息
- 修改 CreateIconModal 标题，根据编辑状态动态显示“编辑图标”或“新建图标”

### 🐛 Bug Fixes

- 更新 MyShop 组件：将输入框最大长度修改为 20，并设置默认值为'基于联盟链的全场景目的地云服务开创者'
- Merge branch 'dev' of git.shukeyun.com:scenic/exchange into dev
- Merge branch 'master' into test
- 优化 apiChatCompletion 函数中的消息处理逻辑，简化 JSON 解析过程并移除冗余的日志输出。
- 更新 MyShop 组件：将默认值修改为初始值，并根据输入内容动态设置应用简介和名称
- Merge branch '0516/mask' into test
- 消息中心静态编写
- 添加条件组优化逻辑
- 库存预警逻辑调整
- 修改
- Merge branch '0516/lhy' into test
- 代理商图表数据渲染问题修复
- 选择员工完善删除和确定返回数据对戒
- 删除条件组问题暂时搁置，优化
- 修改订单管理列表操作日志的动作类型为'info'
- 将订单状态'已退款'修改为'退款成功'
- Merge branch '0516/bugfix' into test
- 报表模块部分问题修复
- 报表排名已加上
- 问题已修复删除负责人
- 标记为全部已读对接完成
- Ui 问题修复
- 修复 UI 问题
- 修改
- 在策略模态中直接传递所有适用分组 ID 列表，并修正注释中的商品 ID 描述
- 经销商，代理商审核驳回原因过长，列表文案重叠
- 【消息通知】点击“标记所有为已读”则所有信息进入“已读”分类，自动刷新
- 更新支付收银台
- 更新知识库相关接口的主机地址，统一使用 helpHost
- 代理销售授权修改-站内信部分信息显示 null
- 修改价格策略字段
- 分页问题解决
- 查看代理供应商修复
- 修复批次信息没有库存汇总的问题
- 修复支付链接跳转逻辑，添加调试信息
- 处理条件数以及跳转字段对接
- 存在多个条件组时，点击一个删除后会把所有的都删除
- 点击“立即补充库存”跳转至页面报错解决
- 字段变更
- 将所有使用 location.href 的地方替换为 window.location.href，以提高代码一致性
- 更新购票人信息字段，使用 pilotName 和 pilotPhone 替代 buyerName 和 buyerId，以确保数据准确性
- 调整销售分组和代理标签的顺序，以提升用户体验
- 修复定时器的报错，修复再次申请拿不到路由
- 消息模块暂时隐藏展示
- 解决点击展开全部子项问题
- 销售信息默认展开
- 样式修改
- 优化 StrategyModal 组件，调整样式和注释，改善信息展示效果
- 修改方法通过校验
- 重构 NormalPriceContent 组件，使用表单形式管理价格设置，优化状态管理和数据回显逻辑
- 更新价格策略相关组件，修改策略详情名称，增强价格设置模态框的动态价格处理逻辑 #369 #367 #344 #366 #359
- 更新价格策略组件，设置默认选项为'self'，并在日历表格中添加顶部边框样式
- 修改价格策略组件中的开关标签和禁用状态
- 更新 API 接口，统一使用 helpHost 替代 scenicHost，确保知识库和聊天记录相关请求的正确性
- 修改价格策略组件中的开关标签为“禁用”，并添加组合销售弹窗的临时状态管理
- 在价格设置模态框中添加日期选择器的禁用日期逻辑，防止选择过去日期
- 优化价格设置模态框，简化组件结构，更新表单验证信息，确保用户体验更流畅
- 消息问题修改
- 在价格设置模态框中添加弹窗关闭时重置表单验证状态的逻辑
- 修复动态定价 UI
- Tab 页定位优化
- 对接历史数据表格
- 消息和库存模块优化
- 解决入院情况多次请求 bug
- 解决入院图表 goodsId bug
- 推荐标签加上
- 库存预警联调修复
- 联调处理库存预警
- 【店铺管理-评论管理】评论回复问题修复
- 优化评论宽度展示
- 景区名称筛选字段变更
- 列表字段回显问题修复
- 回复时间修复
- 拖拽接口对接
- 注释掉价格数据状态更新逻辑并调整价格历史图表样式
- 拖拽接口修改
- 更新切换旧版本触发保存逻辑
- 关闭弹窗强制卸载 dom，以保证缓存清空
- 智能后台运营-更多功能按钮交互优化
- 订单评价调整顺序
- 添加新的批次提示成功，实际未添加成功
- 移除 prod 环境中 huijingyun.net 域名检测条件
- 替换全局 ENV 变量为 getEnv()函数调用
- 销售授权我问题修复
- 销售授权列表问题
- 增加批量编辑和修复问题
- 解决冲突
- 筛选项查询，列表数据有问题
- 合并
- 保存后，销售价数据未更新
- 增加批量添加批次时默认现售价展开状态
- 销售列表展开问题调整
- 库存批次号查询页面报错
- 重写新建图标提交验证方式，改成表单下红色提示；重写上传按钮交互
- 修复 SVG 加载逻辑，确保使用绝对路径并增加错误处理
- 解决舵式导航预览的问题
- 解决批量删除导航，再次添加时，预览区域显示异常的问题
- 修复 CacheDemo.tsx 中的 ESLint 错误
- 优化 SVG 加载逻辑，确保使用动态 publicPath 并修复错误处理
- 解决舵式样式问题
- 解决 canary 环境图片问题

### 🚜 Refactor

- 直销动态定价集中在一个文件夹里，方便维护
- 更新 ProSvg 组件的 SVG 加载逻辑，优化代码结构并删除无用的缓存演示组件

### 📚 Documentation

- Auto-generate changelog [ci-skip]

### ⚙️ Miscellaneous Tasks

- 注释掉预授信管理相关路由配置，以便后续重构

## [release-v4.2.1] - 2025-04-18

### 🚀 Features

- 添加 AI 接口支持，更新相关配置和依赖 #HLY-385
- 添加店铺详情接口，更新店铺表单字段和逻辑
- 更新店铺表单，替换图片上传组件并添加文件上传逻辑
- 添加企业绑定景区验证功能，更新店铺表单逻辑以控制推荐选项显示
- 在 apiChatCompletion 函数中引入消息队列和流控制，优化消息处理逻辑

### 🐛 Bug Fixes

- 修复角色 ID 传递错误，更新 API 参数
- 更新分销商店铺 API 调用，添加 distributorId 参数
- 更新分销商店铺 API 调用逻辑，优化 recommend 字段处理
- 更新 Jenkinsfile，优化 CI_COMMIT_BRANCH 和 BRANCH 变量的赋值逻辑
- Test
- Merge branch 'fix/ai' into test
- 移除多余的图片元素，简化 AI 服务组件
- 更新订单状态描述，优化用户体验
- Icon

### 🚜 Refactor

- 优化 apiChatCompletion 函数，重构异步处理机制，改进消息处理逻辑
- 移除 orderType 常量，统一使用 orderTypeEnum，更新相关引用

## [release-v4.2.0] - 2025-04-03

### 🐛 Bug Fixes

- #CSZ-281
- 修复问题 #CSZ-607
- Merge branch '0403/hotfix' into test

### 🚜 Refactor

- _(api)_ 网关统一 #XINFANSHI-836

## [release-v4.1.1] - 2025-03-20

### 🚀 Features

- #HLY-218 #HLY-220 #HLY-216 易旅通新增引导功能
- #HLY-219 #HLY-221 联调新手任务和提升建议
- #HLY-217 #HLY-255 新增新手指南缩略弹窗，引导联调
- 链接修改

### 🐛 Bug Fixes

- COMMON_API_HOST
- 报表导出异常
- 修改链接
- 删除不必要的代码
- 修改引导空数据的逻辑，以及企业注册调用接口的流程
- 修复
- #HLY-219 处理订单完成和供应商的引导数据
- 优化
- #CSZ-363 #CSZ-364 #CSZ-373 修复 bug
- #CSZ-352 处理 UI 验收问题
- #CSZ-371 #CSZ-369 修复引导的 bug
- 修复问题

## [release-v4.1.0] - 2025-03-11

### 🚀 Features

- Init
- Response
- _(模块)_ 添加了个很棒的功能
- 角色管理
- 订单列表添加企业筛选
- Add eslint
- Debug
- 路由改为 history 模式
- _(角色管理)_ 权限改为树形结构选择
- Prod add https
- Sync
- 添加 退款单 查看权限
- SeachTree
- Add yarn.lock
- 初始化接口异常时，跳转到异常页
- 退回登录页带参数
- 1.角色管理移动到用户中心 2.添加未设置密码提示 3.增加新建账号密码复制 4.新增同级部门功能 5.新增数据迁移功能
- Welcome page charts
- Change chart date
- Pull dev into feat/chart
- 注释首页图表
- Pull origin dev into feat/chart
- 添加路由
- 邀请基本完成
- 审核管理
- 枚举修改
- 添加环境变量
- 操作日志埋点
- 操作日志添加内容对比
- 店铺佣金报表
- 添加禁用功能
- 价格策略
- 请求拦截器添加全局参数
- _(订单管理)_ 添加登录账号筛选项
- 修改线上域名
- 新用户赋予默认权限
- 禁用用户并退出该用户登录状态
- _(订单模块)_ 票务信息列表添加旅游场景名称
- _(店铺管理)_ 价格总计改为接口获取
- 直销价格策略
- 添加头像功能
- 电商邀请完成
- 微服务权限新接口
- 头像显示
- 权限相关接口添加 appId
- Cas 统一登录人数
- 按支付方式待联调
- 联调个人交易统计-按支付方式接口
- 优化一下代码
- 修改一下
- 设置默认一个月
- 添加店铺 ID
- 数据报表优化需求
- 补充结算方式类型
- 添加结算方式
- 添加财务账单的基本页面
- 处理一下财务账单菜单权限
- 联调财务账单接口
- 处理一下结算方式的映射
- 区别一下生产的链接
- 修改一下日期
- 现金账单静态页面
- 现金账单联调
- 添加本地调试的线上配置
- 结算方式添加枚举值
- 自定义 403
- 用户管理所属企业现在当无部门时显示为公司名称
- Yml
- 所属企业冲突问题
- 修复权限重叠问题
- 去除首页欢迎语
- 订单增加手机号筛选
- 新增角色权限时权限菜单为空
- 易旅通导入的商品可以分时段查看库存量
- 易旅通导入商品时需要标识是否已导入
- 替换 logo
- 更换 logo
- 修复直销商品、公池导入查询缺少参数的问题
- 修改企业下拉框企业显示不全的问题
- RiskControl
- 旅游卡全局替换成权益卡
- 佣金账单增加前结算页面 & 财务账单增加订单详情
- 菜单调整
- 结算佣金接口联调
- 更新 readme
- 跳转售票窗口可指定店铺
- 结算方式新加【银联商务 wap 支付】
- 检测低版本浏览器
- 分销商的身份问题
- 第一版完成
- 重构票务库存和接口
- 对接用户列表接口更改
- 添加结算方式：窗口售票 pos 支付
- 店铺员工授权列表
- 添加自助售票字段
- 新增外链字段
- 添加批量权益卡查询页
- 增加批量增加第一步
- 添加批量导入的模板；添加第批量导入第二页信息列表
- 完成批量新增功能
- 完成下载错误信息文件功能
- 完成界面样式
- 添加输入成功与失败的提示；模板文件的下载
- 添加本地搜索过滤功能
- 增加详细分页
- _(2709)_ 禁用经销商分组经销商不支持采购
- 操作日志
- 操作日志初步完成
- 编辑详情
- 添加请求唯一标识符
- 接口/simpleGoods/pageList 入参增加 isChain 上链状态
- 修改智旅链浏览器地址变量
- 添加交易所标签
- 预授信管理
- 采购订单添加重新上链按钮
- 添加总计行样式
- Fix logic for adding total item to stock list
- Update ChainModal to open transactions tab with params
- Update getProductListReq to use getUnifyPullDownStock API
- 新范式第五次迭代
- 增加端口管理官网推荐按钮有关逻辑； https://git.shukeyun.com/new-paradigm/workflow/-/issues/250
- 完成官网推荐按钮有关逻辑联调； https://git.shukeyun.com/new-paradigm/workflow/-/issues/250
- 仅商品启用及上架状态下可打开官网推荐按钮 https://git.shukeyun.com/new-paradigm/workflow/-/issues/250
- 首页、框架搭建
- 完成店铺管理
- 财务账单
- 完成 UI 升级
- 修改弹窗为二级界面 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/577
- 二级界面移到顶层同级 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/577
- 解决数据刷新问题 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/577
- 开始本地接对销售授权新字段，完成分页修改 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/577
- 添加销售授权副表新字段，添加删除时的库存判定 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/577
- 增加添加表格新字段 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/577
- 增加表格内单行编辑 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/577
- 调通修改和删除的接口 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/577
- 调通新增的接口 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/577
- 副表价格修改同步 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/577
- 修改采购订单字段 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/565
- 完成副表数据同步修改保存 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/577
- 完成下拉框选值 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/580
- 修改采购列表门票样式；增加售票详情过滤 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/580
- 修复传参不正确问题 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/580
- 完成 采购订单、取消订单和导出 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/580
- 完成 销售订单表结构更新与数据导出 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/580
- 修复字段对应不上，过滤时间不正确问题 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/580
- 修改产品类型；补充过滤条件下拉框功能; 修复https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/608 有关问题
- 增加下拉框搜索过滤 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/607
- 同步第七次迭代代码 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/5
- 添加取消订单 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/328
- 移动官网推荐到弹窗内 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/815
- 添加金融系统跳转功能 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/815
- 金融系统按钮样式 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/815
- 更新金融系统地址 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/815
- 增加官网推荐按钮交互 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/815
- 对接官网推荐接口 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/815
- 第九次迭代，字段添加
- 商品管理、组合商品兼容分时
- 兼容分时---页面兼容商品名称和票种
- 窗口交易报表优化
- 窗口交易报表导出增加企业 id
- 店铺管理-订单详情（优化统计）
- 处理 formdata 上传的问题
- 新增 CustomErrorBoundary
- 403 页面处理
- 采购订单、销售订单新增区分交易所的页面
- 新增枚举
- B 端采购接口联调
- TicketAgent/price 新增 unitId
- 添加用户申请加入企业的路由
- 删除 GitLab CI 配置文件并添加 Jenkinsfile 以支持 CI/CD 流程
- 添加集合使用开关功能及相关提示信息

### 🐛 Bug Fixes

- Merge
- Add favicon.ico
- 修改 title
- 修改脚手架
- Order bug
- Order time bug
- 增加统一错误处理
- 去除易旅通字眼
- Merge branch 'dev' of git.hqshuke.com:scenic/exchange into feat/order
- Purchase
- 挪动 global.tsx 方法到 tool.ts
- 移除跳转方法
- 修改方法名 getUniqueId
- Merge branch 'dev' of git.hqshuke.com:scenic/exchange into dev
- Merge branch 'dev' of git.hqshuke.com:scenic/exchange into feat/distribu
- 采购订单详情页
- Merge branch 'feat/distribu' of git.hqshuke.com:scenic/exchange into feat/distribu
- 优化按钮样式
- 改为 browserHistory 路由方式
- Fix: Merge branch 'dev' of git.hqshuke.com:scenic/exchange into feat/order
- Add org
- Merge branch 'feat/order' of git.hqshuke.com:scenic/exchange into feat/order
- 路由
- Merge branch 'feat/distribu' of feat/distribu
- 权限修改
- 添加功能的权限
- Canary
- Merge branch 'dev'
- 权限 login bug
- Merge branch 'feat/distribu' of e into feat/distribu
- Merge branch 'dev' of feat/distribu
- Merge branch 'dev' of feat/order
- Merge dev
- Add Announment
- 重构架构
- Merge remote-tracking branch 'origin/dev' into dev
- Merge commit 'e2c9185369c5d8bb09a4a9048de16cee9cdae4cf' into dev
- 修复登录问题
- 替换域名
- Add announcement
- Org tree
- Org
- Merge branch 'dev' of into feat/order
- Merge branch 'dev' of into feat/order
- Merge branch 'feat/order' of git.hqshuke.com:scenic/exchange into dev
- Config
- 首页公告
- Merge branch 'dev' of git.hqshuke.com:scenic/exchange into feat/user
- Origin dev
- 添加环境变量-h5 域名
- Merge branch 'dev' of feat/order
- Merge commit '183c9ec7c5a6d1b4d3e514aeedc556b3455f4c26' into feat/order
- Merge commit 'd35b1b2558f5bf44578730bc8fe3b9266956fd52' into feat/order
- Merge commit 'd1ef35247074ad4ec6c29cfc507142d7e823209c' into feat/order
- Merge commit 'fbd7a0b46828aa1de89c5c19db43dfad245fcdaf' into feat/order
- Org Tooltip
- Dubug
- Merge commit '24edc519fefaa3e38b2b8b49f59b1c2b55c5ac7f' into dev
- Merge commit '763265f723937e8ef467c4c1745bfa56a1a1af6f' into dev
- 去除登录路由
- 1
- 2
- 企业认证授权，配置产品，进退货
- 销售订单
- 优化统一登录
- Merge commit '1e6cf872d802ed2fdb27a33a48a1c72d26bfb3c8' into dev
- 合并
- 优化启动命令
- 链上支付
- 采购退单
- 合并 dev
- 合并 test
- 合并 master
- 配置代理产品
- Prod
- Merge branch 'master' of git.hqshuke.com:scenic/exchange into dev
- Deubg
- Update package.json
- Lock
- 店铺单品套票佣金显示校验优化+eslint
- 同步 lock
- 组合票
- 认证支付环境变量
- 固定依赖版本
- 佣金报表
- 合并冲突
- 解决切换企业权限不更新问题，优化获取企业逻辑
- 组合票图片，价格，营销内容
- Merge branch 'test' of git.hqshuke.com:scenic/exchange into feat/order
- Dev
- Withdrawal function
- 组合销售，店铺订单
- 票号复制
- 进货
- Update @pro-form version
- 退单票号缩进，导入单品组合票区分
- 发包
- 订单类型，票种类型枚举
- 优化用户名
- Master
- 收款场景，员工店铺权限
- Organization 显示
- 收款场景关联商户号
- _(首页)_ 添加售票窗口入口
- 企业权限
- 佣金校验，商品图，营销内容，订单时间区间
- 佣金报表筛选接口
- Dex
- 更替收款场景接口
- 授权列表参数名更改
- 店铺商品列表新增库存字段
- 修改环境变量 MERCHANTS_URL
- 新增售票终端类型
- 订单 购票终端
- Merge branch 'degit.hqshuke.com:scenic/exchange into feat/order
- Merge feat/order
- 收款场景错误拦截
- 订单状态枚举
- 店铺员工权限，企业下拉列表
- 屏蔽组合产品
- 用户审批
- 销售价顺序，图片裂开
- 放开员工销售权限
- 放开员工销售权限，组合票，组合图片
- Window 表表
- 店铺装修，商品标签设置
- 联系方式
- 优化代码
- Merge commit '19f71653543a6df41938ead3eaed736bd450edb2' into feat/order
- 一次暂存起来
- Merge branch 'window/order' into feat/order
- 经销商分组管理接口改动
- 店铺和代理商接口改动
- 找回装修店铺接口，推荐商品
- 进货字段，代理标题
- 旅游卡进货表，智能填充导入单品
- Mergde dev
- Merge commit '421a993854d4c98c72b1b75c10fde064dfebe03e' into feat/order
- SellerId
- Common
- 链下支付
- 配置产品信息取消分页
- 组合商品
- 取消代理商配置商品分页
- 代理商配置产品列表
- Test - 冲突
- 更新旅游卡枚举
- 产品类别，商品票种
- 店铺商品列表类别
- 商品类别，旅游卡订单
- 出卡时间
- 出卡时间，状态
- 电商创建企业标识，店铺订单权益字段，代理分组公共池，导入商品。
- 对接授信接口
- 授信模块
- 结算模块
- 6/8
- 2324
- 完成授信
- Merge branch 'dev' into feat/settlement
- Bug 修复
- Bugfix
- 订单详情新增权益卡 ID 字段
- 单品，组合预约时间，购买时间，入园时间
- 创建店铺添加去实名认证
- Banner 接口改动
- 整理代码
- Merge branch 'dev' of gt.hqshuke.com:scenic/exchange into feat/user
- 添加权限
- 不是 http 开头的地址添加 http://
- 店铺订单实际佣金
- Merge branch 'dev' of https://git.shukeyun.com/scenic/exchange into feat/chart
- 一些 bug 修复
- 指定消费商家字段错误
- 购买终端枚举值
- 限制销售价在指导区间
- 已配置产品列表添加参数
- 添加实名认证
- 修复结算系统商户账号错误
- 预览店铺地址修改
- 店铺产品管理票种不显示
- 修复装修店铺标签重复问题
- 修复设置标签空值报错问题
- 首页图表
- 屏蔽首页图表
- 屏蔽
- 配置结算方式
- 配置佣金结算模式
- 调整样式
- 申请
- 完善邀请
- 弹窗和提示
- 注销佣金报表修改
- 添加了操作日志的列表
- Merge branch 'dev' of git.shukeyun.com:scenic/exchange into operation_log
- Merge branch 'operation_log' of git.shukeyun.com:scenic/exchange into operation_log
- 操作菜单对接口
- 修改地址 url 错误
- 修改验证邀请失效
- CoId 取值错误
- 指导价格区间显示错误
- 修改佣金报表字段错误
- 注销测试代码
- 邀请添加企业认证提示
- 去除 console
- 佣金结算模式查询接口缺漏
- 修改枚举值错误
- 查询与配置佣金结算模式加参数 agentType
- 去掉\*号
- 退佣明细调用接口错误
- 结算登记和佣金报表接口反了
- 企业信息--认证状态
- 修改 h5 店铺地址
- 邀请企业认证条件修改
- 添加供应商字段
- 邀请审核通过后关闭操作按钮
- X 修复配置的产品移除后产品仍然存在
- 单位格式问题
- 注销添加代理商结算时间
- 修复店铺 banner 图片失效问题
- 过滤公共分组
- 完善财务管理导出接口
- 创建店铺实名认证外移
- _(操作日志)_ 添加 project
- Merge branch 'operation_log' of git.hqshuke.com:scenic/exchange into operation_log
- 添加邀请拒绝原因
- 微服务埋点
- 拒绝原因
- _(埋点系统)_ 修复没有企业信息 id 问题
- Merge 操作日志
- 个人认证授权
- 打开报表
- 隐藏区块链
- 票务信息分页
- 刷新问题
- 图表更改顺序
- 图表时间排序
- 进货供应商
- 供应商
- 移除上下级
- 放开单品售价限制
- Footer 文案
- Footer 抽离公共组件
- Merge commit '7473c98664377678dd30807778f0961afd0b7ca6' into feat/order
- 修改订单管理
- 下来菜单
- 跳转地址
- 添加下拉菜单样式
- 修改订单、退订信息
- 注释菜单项一
- Merge branch 'feat/order_hu' into feat/order
- 店铺管理-退单-票务信息
- 优化操作日志
- 优化操作日志字段结构
- 操作日志提交 test
- 操作指南
- 替换操作指南 icon
- 重构登录逻辑
- Merge commit 'f0690fe6499ef04a0290d8f08f54de5710eacab2' into operation_log
- 文案修改
- 店铺--订单--退单
- 去除退票时间
- 添加佣金图表
- Bug
- 分销商改为代理商
- 店铺管理商品列表按商品名称查询
- 分组名称，个人授权
- 优化登录逻辑
- Merge branch 'dev' of git.hqshuke.com:scenic/exchange into operation_log
- 修改邀请地址
- 创建组合产品时增加最大最小数量校验
- 修复供应商商品名称字段展示错误
- 佣金没有展示问题
- 路由的调整
- 代理分组 && 我的代理商
- 路由权限
- 加回单品售价限制
- 菜单合并，进货一键复制
- 添加首页欢迎语
- 首页提示标语
- 全脚符&&查看详情
- 修复 查看详情跳转问题
- 修复首页公告显示问题
- 店铺组合商品
- 合并菜单权限
- 修复邀请后跳转问题
- 支付回调，菜单权限，分组名称
- 修改代理商字段
- 退单景区名称
- 菜单名称
- 联系方式图标必填
- 展示图标必填
- 更换所有图片上传组件
- 新增时候图片错误
- 修改景区环境变量
- Merge branch 'master' of git.hqshuke.com:scenic/exchange into test
- 替换 markdown 组件
- 进货链上支付回调
- 取消支付更新订单
- 复制权限
- 电商权限
- 表单数据联动
- Merge from test
- 优化禁用权限控制
- 对接景区数据
- Test 合并冲突
- 修复价格策略 bug
- _(店铺模块)_ 子订单页面，修改佣金字段
- 修复邀请登录调到无权限的问题
- 修复一些 bug
- Merge branch 'test' of git.hqshuke.com:scenic/exchange into test
- 修复小数精确值
- 修复用户管理禁用问题
- 公池导入逻辑
- 无权限状态码判断
- 修改旅游卡佣金
- 发放和获取佣金路由调换
- 取消 403
- 403
- 价格策略改为 salePrice 字段
- 修复价格策略
- 供应商 id 传值错误
- 增加价格策略权限
- 用户审批传值格式错误
- 收支明细，首页权限，鉴权重刷
- 票务库存
- 票务库存搜索
- 分销商产品列表接口变动
- 单品才有商品佣金策略
- 分销折扣区间保留两位小数
- 去掉无权限页面
- 旅游卡显示商品佣金策略、修复 Infinity
- 旅游场景名称，退票一键复制，退货提示
- Del copy
- TODO：临时放开全企业权限
- 认证查看交互，凭证样式防抖
- 启用企业
- 个人认证企业
- _(用户管理)_ 优化文案
- 启用个人企业
- 注释进货价
- 用户审批接口改动
- 导出接口
- 供应商名称字段
- 组合票价格初始化
- Mear test
- 数据报表修改
- Del log
- Merge test
- 企业路由 + 样式
- 订单枚举状态
- 取消进货分页
- 处理佣金策略能看公共资源分组
- 佣金账单单号字段
- 订单枚举
- _(请求拦截器)_ 添加全局参数时，判断是否为空
- 接口鉴权
- _(订单模块)_ 文案修改 下单用户名称 -> 登录账号
- 价格测分组可控
- 上游分销商查看价格策略界面调整
- 团体票枚举
- 风控禁用权限
- 修复操作日志
- 商品名称，旅游场景名称，搜索字段
- 下架商品名称
- _(订单模块)_ 订单状态改为读取枚举 list
- 价格策略、店铺搜索
- 重置密码权限
- 修复个人代理供货商传参类型类型
- 分时退货批次显示 -
- Merge branch 'test' into dev
- 设置价格策略源数据
- 修改组织用户非必填
- 产品名称
- 订单汇总/明细分页 bug
- 票务库存列表分页
- 价格策略产品名称去重
- Seller_idseller_id
- 订单汇总/明细 bug
- 去掉固定佣金
- 进货字段
- 订单汇总权限
- 设置价格策略 bug 修复
- 退单管理枚举
- 订单汇总 bug
- 如果是邀请页，不弹初始化赋权弹窗
- 退单分时
- Meage
- 价格策略增加删除
- 删除代理商公共分组禁用启用操作
- 进货库存数量
- 进货商品详情，导入商品详情，价格策略规则
- 店铺单品添加景区名称
- 组合景区名称不可编辑
- 金额单位，单品详情接口对接，进货商品详情字段调整
- _(退单模块)_ 注释票务信息
- 规则详情
- 单品旅游卡信息
- 权益规则
- 单品新增价格 ID 参数
- 直销隐藏智能填充 AppID
- 进货的旅游卡详情
- 封装 enum 转 valueEnum 的方法
- 修复商品类别和票种
- _(企业管理)_ 解决无法充值问题
- 退票规则预定费率
- 进货旅游卡补全价格信息
- 预订
- 注释头像功能
- 订单汇总 bug
- 邀请完成
- Merge branch 'test' of git.shukeyun.com:scenic/exchange into feat/permission
- 电商邀请授权后没权限
- 店铺类型显示错误
- 商户账号
- 邀请 type 改为 031
- SettlementId
- CoId
- 邀请用户成功管理员接口，添加 appId
- 初始化权限增加传参
- 默认权限添加 appid
- 修复票种显示
- 价格策略调试
- 价格策略修改
- 修复一下价格展示问题
- 修改字段
- 修改退票接口参数
- Merge branch master of git.hqshuke.com:scenic/exchange into test
- 创建工单增加 phone
- 风控完成
- 区块链交易展示
- 更换数据源
- 出退票列表增加时间参数
- 删除多余代码
- 按供应商增加 agent_id
- 修改面向用户 bug
- 合计统计全部数据
- 隐藏风控配置按钮
- 修改列表合计样式
- 退单管理分页
- 更换一下接口和参数
- 干掉多余注释
- 处理查询账单明细结果为空时列表显示异常问题 & 添加注释
- 获取当前企业员工接口新增禁用系统类型字段
- 解决冲突
- 更换字段
- 放开风控
- 账单明细修改传参
- 修改明细筛选条件
- 修复英文日历问题
- 修改数据源
- 处理下精度问题
- 处理一下交易方向不重置问题
- 处理 bug
- 修改筛选时间传参
- 处理一下账单明细的数据结构
- 删除组合票一些字段展示
- 退单管理样式修复和字段对齐
- 修改创建时间传参问题
- Mergen
- 账单统计列表新增企业 id 字段
- 新增企业 id
- Nft 跳转区分环境
- 修复添加用户缺失 appid
- Merge test
- 退单 bug 修复
- 调整宽度
- 改英文逗号为中文
- 修改单票显示错误
- 企业和个人权限接口参数错误
- 兼容一下旧格式的 JOSN 字符串
- 修改 bug
- 区块链交易弹窗
- 进货 bug
- 修改文案
- 兼容大数据接口返回书记格式
- 删除多余文件
- 大数据成功 code 码改成 2000
- 大数据成功 code 码改成 20000 | 20001
- 首页报表兼容大数据接口 code 码
- 修改订单状态枚举
- /role/permission 接口添加 appId 字段
- 修改传参
- 屏蔽旅游卡上链票
- 电商微服务授权接口修改
- 修改价格信息
- 修改一下获取企业 id 的方式
- 20000
- 个人认证接口修改
- 更改结算地址
- 修复 /bill/billMerchant 拼错问题
- 企业授权
- 解决供应商管理显示问题
- 开通微服务
- 订单枚举搜索，一键应用样式
- 修改权限管理菜单为空的 bug
- 提现按钮事件重复
- 加空格
- 修复生成账单 bug
- 修改订单管理字段
- 结算方式
- 修改时间格式
- 删除添加代理经销商
- 编辑角色的权限时，不作任何操作点击确定，该角色的权限会消失
- 去掉后结算退佣明细
- 枚举值加空格
- 备案号加空格
- 修改店铺管理库存详情显示判断条件
- 删除测试代码
- 修复价格策略显示问题
- 修改格式
- 修改佣金比例显示问题
- 修改时间显示格式
- 拦截直销代理商品组合
- C 端订单管理样式调整
- 收支明细生产环境更换数据源
- 修改前结算筛选条件
- 修改代理经销商查询接口
- 支付时间筛选添加默认时分秒
- 优化店铺管理后结算列表
- 增加支付方式
- 删除退佣明细字段
- 商品名称
- 修复用户权限文字样式问题
- 采购订单号和销售订单号统一换成子订单
- 移除商品主图，更新禁启用状态
- 我要退货参数修改
- Merge branches 'test' and 'test' of git.hqshuke.com:scenic/exchange into test
- 订单详情接口参数修改
- 店铺商品列表状态，财务账单收支明细
- 店铺商品状态
- 权限菜单排序
- 撤回收支明细
- 展示上下架状态
- 恢复收支明细
- 导入商品名称
- 财务接口统一处理
- 交易方向
- 采购退单详情
- 店铺管理、销售管理权限
- 修改菜单名字
- 退单查询 bug
- 修改枚举值
- 修复组合商品名称不显示
- 组合票票种展示更改
- 修复枚举值
- 增加订单类型参数
- 放开直销商品与代理商品不能组合限制
- 修复文案
- 文案修复
- 再次修改文案
- 暂存
- 创建店铺默认名称改成易旅宝
- CoName 参数错误
- 邀请地址栏参数整理
- 参数
- Merge branch 'test' into feature/3-14
- 给下级商品定价
- 修复创建组合产品商品名不显示
- 删除进货列表产品列表参数
- 修复销售退单详情 table 传参错误
- 订单金额 0 元走零元购逻辑
- 新增删除已下架的店铺商品
- 修复 dev 环境财务账单后台接口地址错误
- 环境变量，结算开户
- Merge branch 'test' into canary
- 准生产环境
- 修复订单时间
- 结算认证路由
- 优化本地代理
- 交易报表商品名称
- 第二版销售订单和退单合并完成
- 修复销售退单未弹窗错误
- 销售退单详情增加退单号
- 修复参数错误
- 修复弹窗错误
- 修复 key 值错误
- 修复财务报表接口地址问题
- 接口修改
- 替换首页报表接口
- 邀请限制无法选择自己
- 修复添加分组和通过申请逻辑错误
- 更新认证状态
- 解决 切窗口换登录账号第一次进入报无权限
- 修复退货总量未清空问题
- 企业管理更新状态
- 处理 null 数据错误
- 最大可预订天数 表单校验
- 最大值和精度限制
- 修复窗口交易报表-查看明细显示错误
- 修复店铺管理组合票商品名称不显示
- 销售授权配置产品时旅游场景名称显示错误
- 修复收支明细部分查询参数错误
- 经销商分组详情接口增加参数
- 经销商分组列表参数修改
- 收支明细查询
- 参数此错误
- 撤销财务账单
- 窗口交易报表用户账号查询
- 新增 0 元购支付方式
- 禁用单品商品名称修改
- 修复风控列表错误
- 结算明细名称
- 还款记录结算明细名称
- Package
- 修复收支明细查询参数错误
- 订单分页修改
- 修复经销供货商列表移除概率不刷新问题
- 0 元购进货列表更新
- 修复售票记录售票时间格式错误
- 修复佣金策略 key 值错误
- 更新首页数据统计接口
- 修复票务信息展示未分页
- 修复菜单错误
- 修复购票数量错误
- 用户管理接口对接
- 新增环境变量，修改首页图表数据接口
- 修复用户禁用状态
- 店铺管理 - 优化输入凭证体验
- 处理【用户列表】下点击修改用户，会默认查询全部用户的问题
- 首页 前往景区列表，筛选当前企业下的景区
- 帮助中心权限，角色名称限制
- 单号格式校验
- 修复财务账单收支明细页面错误
- 修复订单改动的错误
- 数据统计订单修改
- 修复订单详情权益卡不展示错误
- 修复订单详情权益卡展示错误
- 修复订单详情展示
- 权限整合接口对接
- 店铺商品接口替换
- 用户下拉列表接口替换
- 店铺接口改动
- 字段错误
- 删除无用接口和文件
- 订单一票多人信息修改
- 1. 全局面包屑；2. 全局表格配置；3. 供应商管理 UI
- 首页
- 销售分组、销售管理、价格策略
- 图表接口对接
- Ui
- 图表单位、表格滚动
- 首页、合作伙伴管理
- 店铺订单退票报错
- C 端业务管理、财务管理
- 景区名称更换
- 修复订单详情退单详情
- 修复图表地址错误
- 修复时间格式
- 修复时间格式错误
- 二级页面
- 企业管理
- 店铺管理二级页面
- 更新组合商品接口，商品详情响应式
- 店铺权益卡商品详情
- 枚举标签，店铺员工列表
- 结算明细，购卡须知
- 修复授信名称筛选项查询
- 修复佣金显示错误
- 订单金额精度
- 删除支付方式排序
- 删除退货单
- 结算明细详情
- 修复分页接口
- 修复多日票展示
- 修复销售授权数据不显示
- 修复销售授权错误
- 添加打包时间
- 修复报错
- 删除 sort 排序
- 全局统一样式
- 企业管理 UI 交互升级
- 用户中心 UI 交互升级
- 系统设置 UI 交互升级
- 修复店铺附加展示异常、导入商品数据遗留
- ProModal、导览管理
- 导览类型枚举
- 重构了操作日志的存储数据结构
- 供应商管理佣金策略门票价格显示问题
- 修复店铺取值错误
- 文章增加修改查看
- 反馈信息查看与删除
- 添加文章排序功能
- Merge branch 'dev' of git.shukeyun.com:scenic/exchange into dev_ban
- 完成文章排序功能；增加跳过统一请求拦截
- 导览链接图片数量
- 外部链接图片限制
- 修复订单状态
- 增加文章长度
- 修改文章名称显示最大长度
- Merge branch 'test' of git.shukeyun.com:scenic/exchange into dev_ban
- 文章外链
- 权益卡续费
- Merge branch 'dev' of gt.shukeyun.com:scenic/exchange into dev_ban
- 屏蔽文章管理
- 内部链接新增图片字段
- Merge branch 'dev' into canary
- 修复首页图表前后结算错误
- 放开文章管理
- 导览链接不可编辑
- 待审核才让打操作
- 解决https://git.shukeyun.com/scenic/workflow/-/issues/2963
- 解决https://git.shukeyun.com/scenic/workflow/-/issues/2962
- 解决https://git.shukeyun.com/scenic/workflow/-/issues/2978
- _(2975)_ 优化权益卡入园日期
- Https://git.shukeyun.com/scenic/workflow/-/issues/2990
- 解决 https://git.shukeyun.com/scenic/workflow/-/issues/2983
- 解决上传错误提示不正确问题
- 解决 https://git.shukeyun.com/scenic/workflow/-/issues/3015
- 导览排序
- 优化导览排序
- 支持店铺名称编辑、长度限制
- 删除商品出错
- _(2708)_ 列表中退货信息需要跟上面字段对齐
- _(3036)_ 增加字数限制
- 财务账单后端接口对齐
- Https://git.shukeyun.com/scenic/workflow/-/issues/3050
- 添加查询中断
- 主页装修
- Yarn https
- 主页设置
- 批量删除
- 页面备注长度限制
- Https://git.shukeyun.com/scenic/workflow/-/issues/3097
- 描述样式
- Url
- 主页装修路由
- 主页不可批量删除
- 修复枚举问题
- Update copyright format in Footer component
- _(3150)_ Canary 环境首页图表接口配置错误
- 页面设置菜单合并
- _(3175)_ 价格策略设定价格优化
- Dev-0229
- 模板选择弹窗
- 页面装修批量删除新增取消选择功能
- 店铺导航
- 隐藏店铺导航、全店风格
- _(3169)_ 修复财务账单报错
- 店铺导航排序
- 页面装修基础模板
- 购票终端枚举
- 修复店铺装修 key 值
- 修复查询批量权益卡没传企业 ID 的问题
- _(3132)_ 人交易统计页面店铺名称回显错误
- 设置价格策略直销添加参数 goodsId
- 售票终端类型增加 OTA
- 售票详情接口添加参数
- _(3221)_ 菜单更改
- Add react-color and ShopStyle component
- Fix shopStyle setActiveColor bug
- 主页模板
- _(3152)_ 我授信的账户进入页面提示商户号不能为空
- _(3220)_ 财务账单问题汇总
- 窗口交易报表
- Merge branch 'dev-0314' of git.shukeyun.com:scenic/exchange into dev-0314
- Remove commented out code for "全店风格" tab in Fit.tsx
- 更新结算商户中心地址
- Pull
- 修复财务账单总数
- 0314
- 店铺导航 UI、交互优化
- 导航接口
- Refactor code for displaying and copying ticket numbers
- 店铺配置接口
- 全店风格
- Merge branch 'dev-0328' of git.shukeyun.com:scenic/exchange into dev-0328
- Update shopConfig and refactor shopStyle component
- 修复切换店铺风格回显异常
- 店铺导航重置功能
- 分批改色模板 1、2
- 分批改色模板 3
- 分批改色模板 4
- 店铺导航、店铺风格
- 修复店铺风格数据读取异常
- 店铺导航接口字段整理
- 修复店铺装修字段获取异常
- 店铺管理
- 财务账单添加默认当月查询
- 公共路径
- Svg 插件路径配置
- 报表导出（订单管理、退单管理）
- 修复财务账单总数接口
- 修复订单支付
- Add box-shadow to template_img and update colorList in shopStyle.tsx
- 操作日志 bug
- 修复财务统计文案
- 操作日志修复
- 报表导出（退单管理、佣金报表）
- 首页图表问题和检票点问题
- 修复店铺佣金报表日期问题
- 用户账号限制修改
- 报表导出
- 修改 ProTable 异常
- 报表导出（窗口交易报表）
- 文章管理回显修复
- 修复导览查询异常
- 修复销售渠道
- 下单时间格式修改
- Merge remote-tracking branch 'origin' into dev-0328-fei
- 修改 canary 环境图片地址配置
- 重发短信修复
- 更新报表导出（11 模块）
- 修复报表总数
- 移除列数据持久化、修复列 key 重复
- 报表导出 - 更新组件、新增 moduleFlag
- 修改 config 配置
- 页面装修模板
- 导航模板配色
- 模板选择图标
- Svg 调整
- 价格策略操作日志
- 修复组件高度异常
- 状态栏新增头像、帮助中心
- 修复报表参数
- 批量导入权益卡问题
- 退货单参数错误
- 参数修改
- 修改供应商
- 限制我要退货
- 操作日志 fix
- _(3371)_ 退票状态修复
- _(3395)_ 票务分销商下拉列表修复
- Merge branches 'canary' and 'canary' of git.shukeyun.com:scenic/exchange into canary
- Merge branch 'canary' into test
- 账单产品类型展示修改
- 传参修改
- 报表导出 - 窗口交易报表
- 文章详情
- 供应商下拉去重
- 订单状态增加
- 店铺管理权限
- 修改默认时间格式
- 人数/已核销总次数
- 删除查看区块链交易金额接口
- 修改检票设备
- 操作日志文案修改
- 店铺导航风格样式
- 单元格大小限制
- 删除法人相关信息
- 区块链门票字段添加
- 修复报表导出用户账号异常
- Refactor shop navigation and shop style components in MyShop page
- 修复接口报错
- Refactor WindowTrading component to use count object for total price and total people
- Merge branch 'master-debug' into test
- Merge branch 'test' of git.shukeyun.com:scenic/exchange into test
- 交易报表用户账号搜索改为下拉选择
- 添加区块链弹窗信息
- 修复商品导入的数字资产展示
- 字段修改
- 去除智旅链浏览器
- 解决订单详情 bug
- 售票员账号查询
- 店铺商品修改数字资产字段名
- 取消区块链支付
- Tooltip
- 添加数字资产字段
- Merge branch 'feat/qkl' into test
- 添加数字资产和区块链弹窗
- 修改数字资产跟上链状态字段
- 修改上链状态为交易上链，并添加标签样式
- 添加区块链交易弹窗
- 修复店铺导入商品数字资产缺少问题
- 修复 apiSimpleGoods 接口传参问题
- 修复采购详情合计 bug
- 更新积分单位、修复表格溢出
- 总计去掉积分单位，统一为元
- 修复进货接口入参问题
- 去除老版区块链交易弹窗
- 更新积分单位
- Push
- 去除重复的退款成功状态
- Update ChainModal tooltip for blockchain information
- 导入商品添加防抖功能
- 价格策略新增字段
- Merge branches 'test' and 'test' of git.shukeyun.com:scenic/exchange into test
- 进货接口添加 isExchange 字段
- 测试 workflow
- 相关优化 workflow
- 修复库存信息展示异常
- 交易上链筛选
- 更新库存信息展示标题为可用库存
- 添加总计行样式时遗漏判断条件
- 修复删除页码异常
- 数字资产商品详情
- 销售退单总计
- 交易上链回显
- 更新机构 ID 字段
- 库存变化记录
- 分销商产品总库存添加过期库存字段
- Https://git.shukeyun.com/scenic/workflow/-/issues/3953
- 修复 https://git.shukeyun.com/scenic/workflow/-/issues/3954
- 修改 https://git.shukeyun.com/scenic/workflow/-/issues/3955
- 修复 https://git.shukeyun.com/scenic/workflow/-/issues/3956
- 优化布局
- 修改电商系统表格空值为 --https://git.shukeyun.com/scenic/workflow/-/issues/3956
- 解决退单查询接口传参问题
- 兼容大数据数据渲染问题
- 修复售票记录无数据问题
- 售票记录总差价改成由前端计算
- 处理订单统计明细里面总数 bug
- Merge branch 'canary' of git.shukeyun.com:scenic/exchange into canary
- 修复总差价问题
- 处理采购、销售差价问题，去除操作人重复项
- 修复总计缓存问题
- 组合票导入商品智能计算并分配库存
- Pull test
- 库存边框
- 屏蔽官网配置
- 修复库存变化记录查询导出异常
- 批次号标识、分页异常
- 放开官网配置
- 取消产品名称筛选
- 还款记录
- 还款状态枚举
- 库存信息样式异常、交易号字段截取
- 过期库存
- 已修改为列表宽度自适应、子列表默认展开 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/321
- 解决 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/134
- 修复 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/143
- 增加当前企业为经销商 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/139
- 添加分页信息 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/408
- 已更新创建时间、退款时间字段 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/135
- 解决面向代理商订单无分页问题 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/21
- 枚举引用错误，已修为引用统一结算方式状态 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/137
- 解决时间过滤失效问题 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/135
- 解决查询组合票剩余库存不显示问题；解决佣金策略价格不显示问题 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/143
- 隐藏官网配置
- 完成数据统计升级
- 官网配置
- 更新接口 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/728
- 更新详细无法查询问题 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/728
- 【销售订单】交易类型显示 bug
- UserId
- Username 改 userId
- 添加提示文字 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/609
- 字段调整 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/638
- 数字输入限制 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/624
- 解决首页大小写问题
- 修改配置
- 修复问题二 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/607
- 添加库存批次号数字输入限制 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/624
- 现可显示多个价格；右上角可关闭 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/643
- 添加服务器要求增加的字段 https://git.shukeyun.com/scenic/workflow/-/issues/4139
- 解决 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/309
- 修复 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/309
- Yarn.lock 还原
- Merge remote-tracking branch 'origin/test' into feat-upgrade
- 修复部分跳转路由错误
- 修复企业下拉框缺失
- 修复样式
- 修复 table 列过长问题
- 修复店铺二级页面
- 解决销售授权消失的问题 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/5
- 解决 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/626
- 增加校验器 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/624
- Merge branch 'feat-upgrade' of https://git.shukeyun.com/scenic/exchange into fix/iteration-7
- 修复 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/650
- 添加总计数量 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/607
- 解决 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/607
- Merge branch 'test' into feat-upgrade
- 修复店铺装修遗漏
- 修复登录
- 修复店铺创建异常
- 修复邀请
- 更改弹窗样式
- 添加过期库存
- 增加编辑时自动展开 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/609
- 完成置灰 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/609
- 完成副表有关改造 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/608
- Merge remote-tracking branch 'origin/feat-upgrade' into fix/iteration-7
- 修复 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/760
- 修复问题二 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/655
- Https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/671
- Https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/657
- Https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/829
- 修复分页错误问题 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/849
- _(835)_ 修复样式和 bug
- 修复销售授权报错
- 调整企业下拉选择宽度
- 无法切换企业
- 区块链地址
- Merge remote-tracking branch 'origin/feat-upgrade' into feat/iteration-8
- 对接接口 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/815
- 添加改变后刷新 https://git.shukeyun.com/research-and-development/scenic/1-huilvyun/-/issues/815
- 更换快捷入口图标 增加未认证提示 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/714
- 对齐表格
- 统一订单搜索枚举值
- 景区跳转问题
- 删除推荐商品
- 修复票务库存 -- 时间展示问题
- Merge branch 'test' into feat/0906
- 【票务库存】查看接口替换入参字段
- 修复【销售分组】选择指定的批次号进行删除，会自动将对应产品下所有的批次号数据都一起删的问题
- Merge branch 'dev' into feat/0906
- Merge branch 'canary' into dev
- 修复销售授权数据量太少报错的问题
- 导入商品分组字段
- 完成企业优化迁移 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/741
- _(889)_ 修复跳转问题
- 修复邀请问题
- 增加 tag 类型
- 调整有效期选择样式 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/744
- Update proxy
- 屏蔽 logo 标题
- 修复生产版本 https://git.shukeyun.com/research-and-development/scenic/2-new-paradigm/-/issues/626
- Merge branch 'test' of git.shukeyun.com:scenic/exchange into canary
- 修复 purchasePrice 没数据时，页面报错的问题
- Merge branch 'feature/hotfix_20240906' into feat/test-publish
- 修改/distribution/stockChangeRecord 接口传参
- Merge branch 'feat/iteration-9' into feature/dev-publish
- 分时库存组件、采购进货
- Merge branch 'feat/iteration-9' of git.shukeyun.com:scenic/exchange into feat/iteration-9
- 组合商品查看新品详情修改接口
- 接口错误
- Revert "Revert "chore: 更换大数据接口""
- 修复【易旅通-企业管理】部分企业点击编辑报错的问题
- Merge branch 'feat/20240920' into feat/test-publish
- 企业信息 --- 查看编辑与慧旅云同步
- Merge branch 'feat/iteration-8' into feat/test-publish
- Merge branch 'feat/iteration-9' into feat/test-publish
- 修复销售授权校验异常
- 商品组合的一些问题处理
- 销售授权等页面 bug 修复
- 修复弹窗重叠的问题
- 修复进货总金额、采购订单详情异常
- 修复销售订单售票记录查询异常
- 卡片样式、分时时段、日期选择限制、退货字眼
- 去掉采购订单、销售订单-售票记录下的定价和差价
- 退货字段调整限制
- Merge branch 'feat/iteration-8' into canary
- 修复企业注册不选择企业报错的问题
- 修复企业注册的必填逻辑
- Merge branch 'test' into feat/publish_20240920
- Merge branch 'canary' into feature/dev-publish
- 修复企业注册编辑传参的问题、修复店铺管理查看传参问题
- Merge branch 'feat/hotfix_0930' into feature/dev-publish
- Ui 优化处理
- 采购进货二级页面、ProModal 组件升级、全局公共样式
- 二级样式
- 二级页面组件、进货二级页面抽离、退货二级页面
- Merge branch 'feat/hotfix_0930' into feat/test-publish
- 票务库存分时详情
- 采购订单详情
- 进退货分时切换交互
- 价格策略---设置价格策略-单买价格改成向上取整保留俩位小数
- 修改单品详情接口传参
- 更新票务库存详情接口
- Merge branch 'feat/iteration-11' into feat/test-publish
- 删除不必要代码
- 修复操作日志 userInfo 取值错误的问题
- 修复全局拦截的参数问题
- 修复 editpop 组件的问题
- 采购订单--采购--商品详情新增分时信息
- Merge branch 'feat/hotfix_0930' into canary
- Merge branch 'feat/iteration-11' into canary
- 优化登录失效页面提示重复的问题
- 价格策略分页
- 票种回显
- 修复退货数量失焦
- 票务信息格式调整
- 可用库存数据缺省
- 可退库存数据缺省
- 优化销售订单子列表的展示问题
- 修复文件上传响应被拦截的问题
- CustomErrorBoundary 默认采用 antd 的 ErrorBoundary
- 修复操作日志的查询问题
- 分页数据删除异常
- 修复退单权益 ID、状态展示异常
- 更新导览管理、文章管理排序接口
- 结算明细详情表格样式、批量发权益卡路由
- 组织架构操作项
- 商品管理 --- 官网推荐展示逻辑调整
- 解决 CustomErrorBoundary 被拦截错误渲染的问题
- Merge branch 'feat/iteration12' into feat/test-publish
- 订单汇总 / 明细 进退货订单详情
- 订单详情展示商品维度信息
- 重构易旅通订单
- 订单状态筛选
- 退单记录跳转区块链地址
- 修复企业注册---关联并更新弹窗的层级问题
- 优化采购订单---风险提示的问题
- 修复 antd edit-table 取消的问题
- 处理销售授权编辑状态数据回显的问题
- 修复采购订单详情回显字段的问题
- Gai
- 隐藏不必要的代码，总差价格式化展示
- 修复采购订单导出默认传参的问题
- 用户管理布局异常
- 修改 isChain 参数
- 改
- 修复【票务库存】入园有效时间未回显的问题
- 修复票务统计 --- 查看分时详情的问题
- 修复入园有效时间展示不正确的问题
- ErrorBoundary 兼容 css 等各类问题
- 库存变化记录字典增加
- 修复采购订单详情-采购信息展示问题
- Merge branch 'test' into feat/fix_test
- 修复 未选中指定部门，列表应该查询所有用户信息
- 金额跟佣金总计格式调整
- 票务信息金额佣金精度
- 公共网关、报表导出、列配置
- 移除构建部署脚本中的成功检查逻辑
- Merge branch master into develop
- 报表导出配置

### 💼 Other

- 申请页面
- 文案调整
- 界面完成
- 文字修改
- 规范金额字段，整理默认排序
- 更新 logo

### 📚 Documentation

- _(模块)_ 更新
- _(模块)_ 更新 2
- _(模块)_ 更新代理商
- _(模块)_ 更新代理商 2
- _(模块)_ 代理商接口
- 更新文档

### ⚙️ Miscellaneous Tasks

- 更换大数据接口
- 支付票数改为人数
- 更新采购订单和销售订单表格列
- Merge branch 'feat/yp' into dev

<!-- generated by git-cliff -->
